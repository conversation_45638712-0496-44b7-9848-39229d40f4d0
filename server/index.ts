import axios from 'axios';
import {
  AI_REQUEST_URL,
  AVATAR_VERSION,
  GAME_REQUEST_URL,
  LOCAL_HOSTS_DOMAIN,
  REQUEST_URL,
} from '../constant';
import store from '../store';
import { setBtcAddress } from '../store/app';
import { getBrowserLangusge, setLocalSession } from '../utils';
import { CitationFile, createStreamRender, StreamMessage } from '../utils/createStreamRender';
import { IPetInfoItem, ORDER_STATUS } from '../constant/type';
import {
  CHAIN_TYPE_ENUM,
  PetShedRecordStatus,
  PetShedRecordType,
  PetStatus,
} from '../constant/enum';
import { eventBus, EventTypes } from '../utils/eventBus';
import { IAvatarMetadata, UsableAddressDataType } from '@/AvatarOrdinalsBrowser/constant/type';
import { IGetBatchNewRankingList, IPetActionReport, IUerBasicInfo } from './index.types';

axios.defaults.baseURL = REQUEST_URL;
axios.defaults.headers['avatarVersion'] = AVATAR_VERSION;
// axios.defaults.headers['domain'] = '040420.uniworlds.io';
// axios.defaults.headers['domain'] = 'https://040420.uniworlds.xyz';

// 设置超时时间 10s
axios.defaults.timeout = 20000;

// 需要监听的API路径
const TASK_UPDATE_PATHS = [
  '/user-action/tree-action', // 砍树
  '/user-action/stone-action', // 挖矿
  '/user-action/fishing-action', // 钓鱼
  '/activity-rank/cmi', // 提交材料
  '/synthesis/create', // 合成工具
  '/pizzaswap/brc20/pay-confirm-send', // 购买道具接口
  '/festival/register', // pizza活动报名
];

// 判断当前请求的url是否rock-score
const isRockScore = (url: string) => {
  return url.includes('rock-score');
};

// 判断当前请求是否需要触发任务更新
const shouldTriggerTaskUpdate = (url: string): boolean => {
  return TASK_UPDATE_PATHS.some((path) => url.includes(path));
};

axios.interceptors.response.use(
  function (response) {
    if (response.data.code === 105) {
      store.dispatch(setBtcAddress(''));
      const address = axios.defaults.headers['address'];
      if (address) {
        setLocalSession(axios.defaults.headers['address'] as string, '', 0);
      }
    }

    // 检查是否需要触发任务更新
    const requestUrl = response.config.url || '';

    if (shouldTriggerTaskUpdate(requestUrl) && response.status >= 200 && response.status < 300) {
      // 成功响应且是需要监听的路径，触发任务更新事件
      setTimeout(() => {
        // 延迟一小段时间再触发，确保服务器数据已更新
        eventBus.publish(EventTypes.TASK_LIST_UPDATED, true);
      }, 500);
    }

    return response;
  },
  function (error) {
    // return Promise.reject(error);
    if (error.response) {
      // 服务器返回错误相应状态码
      const { status } = error.response;
      // 处理500类错误
      if (status >= 500) {
        const message = error.response.data.message;
        // console.error("Server error:", status, message);
        const url = error.response.config.url;
        if (isRockScore(url)) {
          return Promise.reject('101'); // 101表示重新请求rock-list接口
        }
        return Promise.reject(message);
      }
    } else if (error.request) {
      // 网络错误：请求未收到相应
      return Promise.reject(error);
    } else {
      // 设置请求时发生了错误
      console.error('Error setting request:', error.message);
    }
    // 继续将错误向下传递，这样调用方可以自行处理错误
    return Promise.reject(error);
  }
);

export const setRequestHeaders = (session: string, address: string) => {
  axios.defaults.headers['session'] = session;
  axios.defaults.headers['address'] = address;
};

export const getRecommendedFees = () => {
  return axios.get('/basic/recommended-fees');
};

export const getOrderEstimate = (params: {
  address: string;
  feeRate: number;
  metadata: object;
  avatarVersion: string;
}) => {
  return axios.post('/avatar/order-estimate', params);
};

export const orderCreate = (params: {
  collectionId: string;
  pubKey: string;
  feeRate: number;
  avatarVersion: string;
}) => {
  return axios.post('/avatar/order-create', params);
};

export const orderPayment = (params: { orderId: string; signedPsbt: string }) => {
  return axios.post('/avatar/order-payment', params);
};
export const basicLogin = (params: {
  address: string;
  pubKey: string;
  signContent: string;
  signRes: string;
}) => {
  return axios.post('/basic/login', params);
};
export const basicQuickLogin = () => {
  return axios.post('/basic/quick-login');
};

export const getLoginSignContent = () => {
  return axios.get('/basic/login/sign-content');
};

export const getSystemTime = () => {
  return axios.get('/basic/system-time');
};
export const dressCustomCollection = (params: {
  id?: number; // string记录主键，当接口是更新，那么就要传这个参数
  addFlag: boolean; //是否是新增，true表示新增，false更新
  collectionName?: string; //套装名称
  metadata: string;
  avatar: string; //截图
  avatarVersion: string;
}) => {
  return axios.post('/avatar/temporary-collection', params);
};
export const getCustomCollectionById = (id: number | string) => {
  return axios.get(`/avatar/temporary-collection/${id}`);
};

export const deleteCustomCollectionById = (id: number | string) => {
  return axios.delete(`/avatar/temporary-collection/${id}`);
};

export const getOrderList = (params: {
  searchContent?: string;
  status?: ORDER_STATUS;
  pageNo: number;
  pageSize: number;
}) => {
  return axios({
    method: 'get',
    url: `/avatar/order-list`,
    params,
  });
};

export const getCollectionList = (params: {
  state?: 0 | 1; //0表示未上链，1表示上链了
}) => {
  return axios({
    method: 'get',
    url: `/avatar/temporary-collection`,
    params,
  });
};

export const getBrc20Summary = (params: { query: string; pageNo: number; pageSize: number }) => {
  return axios({
    method: 'get',
    url: `/brc20/summary`,
    params,
  });
};

export const getBasicSummaryData = (params: { address: string }) => {
  return axios({
    method: 'get',
    url: `/basic/summary-data`,
    params,
    headers: {
      lang: getBrowserLangusge(),
    },
    timeout: 30000, // 30s
  });
};

export const getInscriptionImageList = (params: {
  address: string;
  pageNo: number;
  pageSize: number;
}) => {
  return axios({
    method: 'get',
    url: `/inscription/image-list/${params.address}`,
    params: {
      pageNo: params.pageNo,
      pageSize: params.pageSize,
    },
  });
};

export const getNftListWallByAddress = (address: string) => {
  return axios({
    method: 'get',
    url: `/nft/list/${address}`,
  });
};

export const wallNftSave = (params: { position: number; content: string }) => {
  return axios({
    method: 'post',
    url: `/nft/saveOrUpdate`,
    data: params,
  });
};

// 临时版本接口
//1. 存储或更新avatar
/**
 * @deprecated
 */
export const saveTempAvatar = (content: string) => {
  return axios({
    method: 'post',
    url: `/temp-avatar/add`,
    data: { content },
  });
};

interface saveAvatarParams {
  content: IAvatarMetadata;
}

export const saveAvatar = (data: saveAvatarParams) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/avatar/interim-storage`,
    data,
  });
};

// 2. 获取我的临时装扮
/**
 * @deprecated
 */
export const getTempAvatar = () => {
  return axios({
    method: 'get',
    url: `/temp-avatar/my-avatar`,
  });
};

interface ResponseData<T> {
  data: {
    code: number;
    msg: string;
    data: T;
  };

  [key: string]: any;
}

type GetAvatarResponse = ResponseData<IAvatarMetadata>;

export const getAvatar = (addr: string): Promise<GetAvatarResponse> => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/avatar/interim-record?addr=${addr}`,
  });
};
//3. 开门得积分
export const tempOpenDoor = () => {
  return axios({
    method: 'post',
    url: `/temp-avatar/open-door`,
  });
};
//4. 种土豆得积分
export const tempPlantPotato = (turnstileToken: string) => {
  return axios({
    method: 'post',
    url: `/temp-avatar/plant-potato`,
    headers: {
      turnstile: turnstileToken,
    },
  });
};
//5 获取我的种植时间以及系统活动时间
export const getTempAvatarActivityTime = () => {
  return axios({
    method: 'get',
    url: `/temp-avatar/activity-time`,
  });
};
//6. 领取成熟土豆
export const claimPotato = () => {
  return axios({
    method: 'post',
    url: `/temp-avatar/claim-potato`,
  });
};

////
// 背包 查询指定用户的物品列表
export const getBagInventoryList = () => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/user-item`,
  });
};
// 背包 给指定物品设置快捷键
export const setEquipmentShortcut = (params: { userItemId: string; shortcut: string }) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/user-item/set-equipment-shortcut`,
    data: params,
  });
};

// 背包 查询指定用户的材料列表
export const getMaterialList = () => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/umi/list`,
  });
};

// 背包 获取可合成列表
export const getSyntheticsList = () => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/user-item/synthetics-list`,
  });
};

// 背包 合成物品
export const mergeSynthesize = (params: { itemId: string; count: number }) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/user-action/synthesize-item`,
    data: params,
  });
};

// 背包 合成物品
export const removeIsNewState = (itemId: string) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/user-item/remove-new-state/${itemId}`,
  });
};
// 通过二级域名获取地址
export const getDomainOwner = (domain: string) => {
  // LOCAL_HOSTS_DOMAIN 有值表示本地测试的域名
  return axios({
    method: 'get',
    url: `/basic/domain-owner?domain=${LOCAL_HOSTS_DOMAIN || domain}`,
  });
};
// 检查当前用户是否持有当前域名
export const checkDomainHolder = () => {
  return axios({
    method: 'get',
    url: `/basic/check-domain-holder`,
  });
};
// 文本转音频获取id
export const textToAudio = (params: { content: string; ttsLang: string }) => {
  return axios({
    method: 'post',
    url: `/tts/request`,
    data: params,
  });
};

// 通过音频id轮询得到url
export const getAudioUrl = (requestId: string) => {
  return axios({
    method: 'get',
    url: `/tts/request-check/${requestId}`,
  });
};

// 上传绑定
export const ttsInfoBind = (params: {
  requestId: string;
  coverImage: string;
  data: any; // 存储avatar的信息，随意什么信息，看你们需求
}) => {
  return axios({
    method: 'post',
    url: `/tts/info-bind`,
    data: params,
    headers: {},
  });
};
// 获取域名绑定的tts信息
export const getTtsInfoBind = () => {
  return axios({
    method: 'get',
    url: `/tts/info-bind`,
    headers: {
      // lang: getBrowserLangusge()
    },
  });
};
// 上传视频
export const uploadVideo = (params: { file: Blob; requestId: string }) => {
  const formData = new FormData();
  formData.append('video', params.file, 'video.webm');
  return axios({
    method: 'post',
    url: `/tts/upload-video/${params.requestId}`,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};
export const getTtsBindList = (params: { pageNo: number; pageSize: number }) => {
  return axios({
    method: 'get',
    url: `/tts/info-bind/list`,
    headers: {
      // lang: getBrowserLangusge()
    },
    params,
  });
};

export const getInfoBindById = (id: string) => {
  return axios({
    method: 'get',
    url: `/tts/info-bind/id/${id}`,
    headers: {
      lang: getBrowserLangusge(),
    },
  });
};
export const setTtsBindStatus = (params: {
  requestId: string;
  status: 'open' | 'close' | 'delete';
}) => {
  return axios({
    method: 'post',
    url: `/tts/info-bind/status`,
    data: params,
  });
};

export const getTtsWhiteList = () => {
  return axios({
    method: 'get',
    url: `/basic/check-domain-holder`,
  });
};

// OpenAI相关接口

// 上传文件到知识库 文件
export const assistantUploadFile = (params: {
  file: File;
  onUploadProgress: (progress: number) => void;
}) => {
  const formData = new FormData();
  formData.append('file', params.file, params.file.name);
  return axios({
    method: 'post',
    url: `${AI_REQUEST_URL}/assistant/upload`,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progress) => {
      const percent = Math.round((progress.loaded / progress.total!) * 100);
      params.onUploadProgress(percent);
    },
  });
};
// 上传文件到知识库 url
export const assistantUploadUrl = (params: { url: string }) => {
  return axios({
    method: 'post',
    url: `${AI_REQUEST_URL}/assistant/upload`,
    data: params,
  });
};

// 2. 获取资料列表
export const getAssistantList = (
  type: 'url' | 'document' // 类型（url、document）url表示网页，document表示文件文档
) => {
  return axios({
    method: 'get',
    url: `${AI_REQUEST_URL}/assistant/list/${type}`,
  });
};

// 3. 提问
// 该接口的请求头对钱包地址做可选参数，如果传钱包地址，那么本接口会做对话历史记录存储，如果是游客，则不存储历史记录
export const getAssistantAnswer = (params: { prompt: string }) => {
  return axios({
    method: 'post',
    url: `${AI_REQUEST_URL}/assistant/answer`,
    data: params,
  });
};

// 4. 删除文件或者链接
export const deleteAssistant = (fileId: string) => {
  return axios({
    method: 'post',
    url: `${AI_REQUEST_URL}/assistant/delete/${fileId}`,
  });
};

// 5. 获取我的聊天历史记录
export const getAssistantChatHistory = (params: { pageNo: number; pageSize: number }) => {
  return axios({
    method: 'get',
    url: `${AI_REQUEST_URL}/assistant/chat-history`,
    params,
  });
};

// 获取快速问答列表
export const getTtsListQuickQa = () => {
  return axios.get('/tts/list/quick-qa');
};

// 根据问题回答答案
export const getTtsQuickQa = (id: string) => {
  return axios.get(`/tts/quick-qa/${id}`);
};

interface IAssistantStreamParams {
  prompt: string;
  onMessage?: (data: StreamMessage) => void;
  onContent?: (content: string) => void;
  onCitationFiles?: (files: CitationFile[]) => void;
  onError?: (error: any) => void;
  onComplete?: () => void;
  address?: string;
}

export type GetAssistantStreamResult = {
  close?: () => void;
  onStop?: () => void;
};

// SSE 流失请求
export const getAssistantStream = (params: IAssistantStreamParams): GetAssistantStreamResult => {
  const { prompt, onMessage, onError, onComplete, address, onContent, onCitationFiles } = params;

  // 创建URL对象并添加查询参数
  const url = new URL(`${AI_REQUEST_URL}/assistant/stream`);
  url.searchParams.append('prompt', encodeURIComponent(prompt));

  // 自定义请求
  const fetchController = new AbortController();
  let streamController: AbortController | null = null;

  fetch(url.toString(), {
    method: 'GET',
    headers: {
      Origin: 'https://3.uniworlds.xyz',
      address: address || '',
      Accept: 'text/event-stream',
      'Cache-Control': 'no-cache',
    },
    signal: fetchController.signal,
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      if (!response.body) {
        throw new Error('Response body is null');
      }
      // 获取响应体的 ReadableStream
      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      streamController = createStreamRender({
        reader,
        decoder,
        options: {
          onMessage: onMessage || (() => {}),
          onContent,
          onCitationFiles,
          onError,
          onComplete,
          dataPrefix: 'data: ',
          dataTransformer: (data: string) => {
            try {
              return JSON.parse(data);
            } catch (error) {
              console.warn('Failed to parse JSON:', data);
              return {
                type: 'content',
                content: data,
              };
            }
          },
        },
      });
    })
    .catch((error) => {
      if (onError) onError(error);
    });

  return {
    close: () => {
      fetchController.abort();
      streamController?.abort();
    },
    onStop: () => {
      streamController?.abort();
      fetchController.abort();
    },
  };
};

export const TwitterAuthService = {
  /**
   * 获取Twitter授权URL
   * @returns 返回授权URL的Promise
   */
  getAuthUrl: async ({
    captchaToken,
    sessionId,
    address,
  }: {
    captchaToken?: string;
    sessionId?: string;
    address?: string;
  }) => {
    try {
      // 使用相对URL确保请求发送到同源服务器，避免CORS问题
      const response = await axios({
        method: 'get',
        url: `${GAME_REQUEST_URL}/twitter/auth`,
      });
      return response;
    } catch (error) {
      console.error('Failed to get Twitter auth URL:', error);
      throw error;
    }
  },

  /**
   * 检查Twitter授权状态
   * @param sessionId 授权会话ID
   * @returns 授权状态
   */
  checkAuthStatus: async (sessionId?: string): Promise<any> => {
    try {
      const response = await axios({
        method: 'get',
        url: `${GAME_REQUEST_URL}/twitter/status`,
      });
      return response.data;
    } catch (error) {
      console.error('Failed to check Twitter auth status:', error);
      throw error;
    }
  },
};

/**
 * 领取装备
 * @param params
 * address 钱包地址
 * session 会话ID
 * type 类型 axe 斧头
 * sw 装备ID
 * @returns
 */

export const receiveActivityItem = (headers: any) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/user-action/receive-activity-item`,
    headers,
  });
};

/**
 * 重新抽取装备
 * @param params
 * address 钱包地址
 * session 会话ID
 * type 类型 axe 斧头
 * sw 装备ID
 * @returns
 */

export const randomReceiveItem = (params: any, headers: any) => {
  return axios.post(`${GAME_REQUEST_URL}/user-action/random-receive-item`, params, {
    headers,
  });
};

/**
 * 上报积分
 * @param params
 * address 钱包地址
 * session 会话ID
 * type 类型 axe 斧头
 * userItemId 用户物品ID
 * treeId 树木ID
 * @returns
 */
export const setReportScore = (params: any, headers: any) => {
  const url = `${GAME_REQUEST_URL}/activity-rank/score`;
  return axios.post(url, params, { headers });
};

/**
 * 获取树木列表信息
 * @returns [{id: string, status: string}]
 */
export const getTreeList = () => {
  const url = `${GAME_REQUEST_URL}/activity-tree/tree-list`;
  return axios({
    method: 'get',
    url,
    timeout: 10000,
  });
};

/**
 * 获取当前用户基础数据信息
 * @returns
 *  twitterFlag: boolean, // 是否绑定twitter
 *  userItemId: string, // 当前持有的工具id，如果是null表示没有持有任何工具
 *  activityInfo: {
 *    "activityType": "axe", // 活动类型
 *    "startTime": 1741708801000, // 活动开始时间
 *    "endTime": 1765468801000 // 活动结束时间
 * },
 * playerInfo:{
 *    "energy": number,
 *    "freeTime": 0,
 *    "totalEnergy": number
 * }
 *
 */
export const getUserBasicInfo = (): Promise<ResponseData<IUerBasicInfo>> => {
  const url = `${GAME_REQUEST_URL}/basic/info`;
  return axios({
    method: 'get',
    url,
  });
};

/**
 * 获取排行榜积分列表
 * @param topN 排行榜积分列表数量
 * @returns
 */
export const getRankingScoreList = (topN: number) => {
  const url = `${GAME_REQUEST_URL}/activity-rank/rank-list?topN=${topN}`;
  return axios({
    method: 'get',
    url,
  });
};

/**
 * 获取矿石列表
 * @returns
 */
export const getRockList = () => {
  const url = `${GAME_REQUEST_URL}/activity-rock/rock-list`;
  return axios({
    method: 'get',
    url,
    timeout: 10000,
  });
};

/**
 * 上报矿石积分
 * @param params
 * address 钱包地址
 * session 会话ID
 * type 类型 pickaxe 斧头
 * userItemId 用户物品ID
 * rockId 矿石ID
 * @returns
 */
export const setRockScore = (params: any, headers: any) => {
  const url = `${GAME_REQUEST_URL}/activity-rank/rock-score`;
  return axios.post(url, params, { headers });
};

/**
 * 获取矿石排行榜列表
 * @returns
 */
export const getRockRankingList = (topN: number, singleType: string) => {
  const url = `${GAME_REQUEST_URL}/activity-rank/rank-list-by-type?topN=${topN}&singleType=${singleType}`;
  return axios({
    method: 'get',
    url,
  });
};

/**
 * 测试error 500状态码
 */
export const testError500 = (params: any, headers: any) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/basic/error`,
    data: params,
    headers,
  });
};

/**
 * 领取狗头彩蛋结果
 */
export const getEggResult = (params: any, headers: any) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/user-action/check-easter-egg`,
    data: params,
    headers,
  });
};

export const getEasterEggResult = (params: any, headers: any) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/easter/whack-a-mole`,
    data: params,
    headers,
  });
};

/**
 * 甩竿获取鱼儿
 */
export const getFish = (params: { userItemId: string }) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/activity-fish/fish-info`,
    data: {
      ...params,
    },
  });
};

/**
 * 上报鱼儿积分
 */
export const setFishScore = (params: { fishRecordId: string }, headers: any) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/activity-fish/rfc`,
    data: params,
    headers,
  });
};

/**
 * 完成彩蛋任务
 */
export const completeFishEggTask = (params: any, headers: any) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/activity-fish/fish-easter-egg`,
    data: params,
    headers,
  });
};

/**
 * 挖石头
 * @param params
 * address 钱包地址
 * session 会话ID
 * userItemId 用户物品ID
 * targetId 矿石ID
 * @returns
 */
export const userActionStone = (params: any, headers: any) => {
  const url = `${GAME_REQUEST_URL}/user-action/stone-action`;
  return axios.post(url, params, { headers, timeout: 10000 });
};

/**
 * 砍木头
 * @param params
 * address 钱包地址
 * session 会话ID
 * userItemId 用户物品ID
 * targetId 矿石ID
 * @returns
 */
export const userActionTree = (params: any, headers: any) => {
  const url = `${GAME_REQUEST_URL}/user-action/tree-action`;
  return axios.post(url, params, { headers, timeout: 10000 });
};

/**
 * 钓鱼
 * @param params
 * address 钱包地址
 * session 会话ID
 * userItemId 用户物品ID
 * targetId 矿石ID
 * @returns
 */
export const userActionFish = (fishRecordId: string, headers: any) => {
  const url = `${GAME_REQUEST_URL}/user-action/fishing-action/${fishRecordId}`;
  return axios.post(url, {}, { headers, timeout: 10000 });
};

/**
 * 提交材料
 * tick 提交到哪个社区
 * sw 加密后的sw
 */
interface SubmitMaterialParams {
  tick?: 'potato' | 'wangcai' | 'TheLonelyBit' | 'Pizza' | 'DomoDucks';
  topN: number;
  resourceList: {
    tag: string;
    quantity: number;
  }[];
}

export const submitMaterial = (params: SubmitMaterialParams, headers: { sw: string }) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/activity-rank/cmi`,
    data: params,
    headers,
  });
};

/**
 * 查询排行榜列表
 * @param params
 * tag 排行榜类型
 * @returns
 */
export const getNewRankingList = (params: { tag: string; topN: number }) => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/activity-rank/rlt?activityTag=${params.tag}&topN=${params.topN}`,
  });
};

/**
 * @description 批量查询
 * @returns
 */
export const getBatchNewRankingList = (params: {
  tag: string;
  topN: number;
}): Promise<ResponseData<IGetBatchNewRankingList[]>> => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/activity-rank/batch-rlt?activityTags=${params.tag}&topN=${params.topN}`,
  });
};

/**
 * 查询领水相关信息
 * /pizzaswap/brc20/community-faucet-info/:tick
 */
export const getWaterClaimInfo = (
  tick: 'potato' | 'wangcai' | 'TheLonelyBit' | 'Pizza' | 'DomoDucks'
) => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/pizzaswap/brc20/community-faucet-info/${tick}`,
  });
};

/**
 * 领取领水
 * /pizzaswap/brc20/community-faucet/:tick
 */
export const claimWater = (tick: 'potato' | 'wangcai' | 'TheLonelyBit' | 'Pizza' | 'DomoDucks') => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/pizzaswap/brc20/community-faucet/${tick}`,
  });
};

/**
 * 预支付接口
 * /pizzaswap/brc20/pay-pre-send/:tick
 */
export const prePay = (tick: 'potato' | 'wangcai' | 'TheLonelyBit' | 'Pizza' | 'DomoDucks') => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/pizzaswap/brc20/pay-pre-send/${tick}`,
  });
};

/**
 * 确认支付接口
 * /pizzaswap/brc20/pay-confirm-send
 */
export const payConfirmSend = (params: any, headers: any) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/pizzaswap/brc20/pay-confirm-send`,
    data: params,
    headers,
  });
};

/**
 * 获取购买道具信息
 * /pizzaswap/brc20/pay-info/:tick
 */
export const getBuyEnergyInfo = (
  tick: 'potato' | 'wangcai' | 'TheLonelyBit' | 'Pizza' | 'DomoDucks'
) => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/pizzaswap/brc20/pay-info/${tick}`,
  });
};

/**
 * 获取捐赠信息
 * /pizzaswap/brc20/donation-info/:tick
 */
export const getDonationInfo = (
  tick: 'potato' | 'wangcai' | 'TheLonelyBit' | 'Pizza' | 'DomoDucks'
) => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/pizzaswap/brc20/donation-info/${tick}`,
  });
};

/**
 * 预捐赠接口
 * /pizzaswap/brc20/donation-pre-send/:tick?amount=100
 */
export const preDonation = (
  tick: 'potato' | 'wangcai' | 'TheLonelyBit' | 'Pizza' | 'DomoDucks',
  amount: string
) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/pizzaswap/brc20/donation-pre-send/${tick}?amount=${amount}`,
  });
};

/**
 * 确认捐赠接口
 * /pizzaswap/brc20/donation-confirm-send
 */
export const donationConfirmSend = (params: any, headers: any) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/pizzaswap/brc20/donation-confirm-send`,
    data: params,
    headers,
  });
};

/**
 * 查询排行榜列表（无需登录）
 * /activity-rank/rlt-public?activityTag=10001
 */
export const getPublicRankingList = (activityTag: string, topN: number) => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/activity-rank/rlt-public?activityTag=${activityTag}&topN=${topN}`,
  });
};

type SynthesisCommonType = 'petResource';

/**
 * 查询合成配方列表
 * /synthesis/list
 */
export const getSynthesisList = (commonType?: SynthesisCommonType) => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/synthesis/list${commonType ? `?commonType=${commonType}` : ''}`,
  });
};

/**
 * 合成装备：需要sw
 * /synthesis/create/10001
 */
export const createSynthesis = (params: any, headers: any) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/synthesis/create/${params.synthesisTag}`,
    // data: params,
    headers,
  });
};

/**
 * 轮训获取不可用信息
 * /avatar//unusable-info
 */
export const getUnusableInfo = () => {
  return axios({
    method: 'get',
    url: `${REQUEST_URL}/avatar/unusable-info`,
    timeout: 30000, // 30s
  });
};

/**
 * 查询掉落物
 * /user-drop-item/list
 */

export const getUserDropItemList = () => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/user-drop-item/list`,
  });
};

/**
 * 拾取掉落物
 * /user-drop-item/pick-up/:id
 * id 掉落位置tag
 */
export const pickUpDropItem = (id: string) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/user-drop-item/pick-up/${id}`,
  });
};

/**
 * 获取任务进度列表
 * /task/progress-list
 */
export const getTaskList = () => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/task/progress-list`,
  });
};

/**
 * 刷新指定任务的进度状态
 * /task/client-refresh-task
 * @param params
 * id 任务进度ID
 * clientRefresh 需要刷新的条件类型
 */
export const refreshTask = (params: { id: string; clientRefresh: string[] }) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/task/client-refresh-task`,
    data: params,
  });
};

/**
 * 领取任务奖励
 * /task/receive-rewards/:id
 * @param params
 * id 任务进度ID
 */
export const claimTaskReward = (params: { id: string }) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/task/receive-rewards/${params.id}`,
    data: params,
  });
};

/**
 * 获取庆典信息
 */
export const getFestivalInfo = () => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/festival/info`,
  });
};

/**
 * 报名庆典活动
 * /task/receive-rewards/:id
 */
export const registerFestival = (params: any, headers: any) => {
  return axios.post(`${GAME_REQUEST_URL}/festival/register`, params, {
    headers,
  });
};

/**
 * 上报庆典活动结果
 * /task/receive-rewards/:id
 * @param params
 * festivalTag 活动tag
 * tagList 拾取坐标数组
 */
export const reportFestival = (params: any, headers: any) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/festival/report`,
    data: params,
    headers,
  });
};

/**
 * 获取庆典活动排名
 * /task/receive-rewards/:id
 * @festivalTag 活动tag
 */
export const rewardFestival = (festivalTag: string) => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/festival/reward/${festivalTag}`,
  });
};

// 用于主动更新任务进度
export const updateTaskProgress = async () => {
  try {
    const response = await getTaskList();
    if (response.data && response.data.data) {
      // 发布获取到的数据
      eventBus.publish(EventTypes.TASK_PROGRESS_DATA, response.data.data);
      return response.data.data;
    }
  } catch (error) {
    console.error('[TaskUpdate] 更新任务进度失败:', error);
  }
  return null;
};

/**
 * 获取可发送的红包代币列表
 * /red-packet/tick-list
 */

export const getRedPacketTickList = () => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/red-packet/tick-list`,
  });
};

/**
 * 获取发送单个红包代笔的sfb数量
 * /red-packet/tick-anchor-amount/sFB___000
 */
export const getRedPacketTickAnchorAmount = (tick: string) => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/red-packet/tick-anchor-amount/${tick}`,
  });
};

/**
 * 红包预发送
 * /red-packet/pre-send
 */
export const preRedPacketSend = (params: any) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/red-packet/pre-send`,
    data: params,
  });
};

/**
 * 确认发送红包
 * /red-packet/confirm-send/6835a3d4d07d114f379c8453
 */
export const confirmRedPacketSend = (params: any, id: string) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/red-packet/confirm-send/${id}`,
    data: params,
  });
};

/**
 * 获取排行榜奖励预览
 * /activity-rank/rac
 */
export const getRankingRewardPreview = () => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/activity-rank/rac`,
  });
};

/**
 * 销毁工具
 * /user-action/destroy-item/68498af1fad8c550af6ec468
 */
export const destroyItem = (params: any) => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/user-action/destroy-item/${params.userItemId}`,
  });
};

export type IUsableAddressResponseData = ResponseData<UsableAddressDataType>;
/**
 * @description 获取可使用哪些avatar装扮资源
 */
export const getUsableAddress = (): Promise<IUsableAddressResponseData> => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/avatar/address-usable`,
  });
};

export interface PlacePetBedResp {
  userItemId: string;
  userItemTag: string;
  positionTag: string;
  slotRecordId: string;
  slotRecordStatus: PetShedRecordStatus;
  waitingEndTime: number;
}

/**
 *
 * @description 宠物窝放置接口
 */
export const placePetBed = (data: {
  positionTag: string;
  userItemId: string;
}): Promise<ResponseData<PlacePetBedResp>> => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/user-pet-item/place`,
    data,
  });
};

interface IGetPetShedInfo {
  _id: string;
  userItemId: string;
  positionTag: string;
  userItemTag: string;
  status: PetShedRecordStatus;
  waitingEndTime: number;
  createdAt: string;
  updatedAt: string;
  petTag: string;
  successAt: string;
  recordType: PetShedRecordType;
  chainType: CHAIN_TYPE_ENUM;
}

export const getPetShedInfo = (): Promise<ResponseData<IGetPetShedInfo[]>> => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/user-pet-item/shed-record-list`,
  });
};

export const getPetList = (firstFlag?: boolean): Promise<ResponseData<IPetInfoItem[]>> => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/user-pet-item/list`,
    params: {
      firstFlag,
    },
  });
};

interface IRetrievePetBed {
  petId: string;
  petTag: string;
  userItemId: string;
  userItemTag: string;
  currentDurability: number;
  nextSlotRecordId: string;
  waitingEndTime: number;
}

export const retrievePetBed = ({
  slotRecordId,
}: {
  slotRecordId: string;
}): Promise<ResponseData<IRetrievePetBed>> => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/user-pet-item/retrieve/${slotRecordId}`,
  });
};

export const pickPetAltar = (data: {
  positionTag: string;
  userItemId: string;
}): Promise<
  ResponseData<{
    positionTag: string;
    userItemId: string;
  }>
> => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/user-pet-item/pick`,
    data,
  });
};

export interface ChangePetStatusRespData {
  followList: {
    _id: string;
    petStatus: IPetInfoItem['petStatus'];
    followSlot: number;
  }[];
  targetPet: IPetInfoItem;
}

export const changePetStatus = (data: {
  petId: string;
  petStatus: PetStatus;
}): Promise<ResponseData<ChangePetStatusRespData>> => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/pet-action/change-pet-status`,
    data,
  });
};
export const changePetAllFollow = (): Promise<ResponseData<any>> => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/pet-action/all-follow`,
  });
};

export const changePetNewFlag = (petId: string): Promise<ResponseData<IPetInfoItem>> => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/user-pet-item/change-new-flag/${petId}`,
  });
};

export const changePetName = (data: {
  petId: string;
  petName: string;
}): Promise<ResponseData<{ petName: string }>> => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/user-pet-item/change-pet-name`,
    data,
  });
};

export const petAxe = (data: {
  petId: string;
  targetId: string;
}): Promise<ResponseData<IPetActionReport>> => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/pet-action/tree`,
    data,
  });
};

export const petPickAxe = (data: {
  petId: string;
  targetId: string;
}): Promise<ResponseData<IPetActionReport>> => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/pet-action/stone`,
    data,
  });
};

export const petFish = (petId: string): Promise<ResponseData<IPetActionReport>> => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/pet-action/fishing/${petId}`,
  });
};
export const getPetFoodList = (): Promise<ResponseData<{ tag: string; stamina: number }[]>> => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/pet-action/food-list`,
  });
};

export const petFeed = (data: {
  petId: string;
  foodList: { tag: string; quantity: number }[];
}): Promise<ResponseData<{ petId: string; currentStamina: number }>> => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/pet-action/feed`,
    data,
  });
};

export const petRelease = (petId: string): Promise<ResponseData<IPetInfoItem>> => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/user-pet-item/release-pet/${petId}`,
  });
};

export const petFusion = (data: {
  firstPetId: string;
  secondPetId: string;
  slotRecordId: string;
}): Promise<
  ResponseData<{
    slotRecordId: string;
    waitingEndTime: number;
  }>
> => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/user-pet-item/evolve`,
    data,
  });
};

/**
 * web3 链上宠物相关
 */

export interface IFeeRateRespData {
  feesRecommended: {
    fastestFee: number;
    halfHourFee: number;
    hourFee: number;
    economyFee: number;
    minimumFee: number;
    updateTime: number;
  };
  generatorSats: {
    inscription: number;
    base: number;
  };
  price: number;
}

export const getFeeRate = (): Promise<ResponseData<IFeeRateRespData>> => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/alkanes-pet/fees/recommended?actionType=generator`,
  });
};
export const petWeb3Generator = (data: {
  address: string;
  pubKey: string;
  feeRate: number;
}): Promise<ResponseData<{ unsignedPsbt: string; orderId: string }>> => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/alkanes-pet/generator`,
    data,
  });
};
export const petWeb3Merge = (data: {
  address: string;
  pubKey: string;
  feeRate: number;
  alkaneIds: string[];
}): Promise<ResponseData<{ unsignedPsbt: string; orderId: string }>> => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/alkanes-pet/merge`,
    data,
  });
};

export const petWeb3Broadcast = (data: {
  signedPsbt: string;
  orderId: string;
  // 创建宠物传
  userItemId?: string;
  positionTag?: string;

  // 融合宠物传
  slotRecordId?: string;
}): Promise<ResponseData<{ txid: string }>> => {
  return axios({
    method: 'post',
    url: `${GAME_REQUEST_URL}/alkanes-pet/broadcast`,
    data,
  });
};

export enum AlkanePetTxEnum {
  // 宠物生成类型： 新生成/融合
  GENERATOR = 'generator',
  MERGE = 'merge',
}

export const petWeb3OrderQuery = (
  txid: string
): Promise<
  ResponseData<{
    confirmed: boolean;
    txType: AlkanePetTxEnum;
    petInfo: IPetInfoItem;
    slotRecord: {
      currentDurability: number;
      petTag: string;
      positionTag: string;
      slotRecordStatus: PetShedRecordStatus;
      userItemTag: string;
    };
  }>
> => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/alkanes-pet/tx-status/${txid}`,
  });
};

export const petWeb3OrderList = (address: string): Promise<ResponseData<string[]>> => {
  return axios({
    method: 'get',
    url: `${GAME_REQUEST_URL}/alkanes-pet/tx-list/${address}`,
  });
};
