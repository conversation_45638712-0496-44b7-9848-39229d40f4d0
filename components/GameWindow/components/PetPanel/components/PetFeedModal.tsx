import Dialog from '@/commons/Dialog';
import { RefObject, useEffect, useMemo, useRef, useState } from 'react';
import styled from 'styled-components';
import { StyledDialogBody, StyledFooter } from '@/components/Basic/ModalContentElement';
import { usePetPanelContextSelector, usePetPanelContextDispatch } from '../context';
import MateriaList, { MaterialListRef } from '@/components/SubmisionV2/MaterialList';
import SlotList, { SlotListRef } from '@/components/SubmisionV2/SlotList';
import { ModalContent, Button } from '@/components/SubmisionV2/SubmissionV2Modal';
import { Close } from '@/components/BasicCommunityModalContent';
import Image from 'next/image';
import { PetInfoSectionMain } from './PetDetailPanel';
import { debounce } from 'lodash';
import { petFeed } from '@/server';
import useUpdatePetListItem from '@/hooks/useUpdatePetListItem';
import { PetInfoItem, PetInfoItemLabel, PetInfoItemValue } from './PetDetailPanel/style';
import { useAppDispatch, useAppSelector } from '@/hooks/useStore';
import { ItemConfig } from '@/game/Config/ItemConfig';
import { getCdnLink } from '@/AvatarOrdinalsBrowser/utils';
import toast from 'react-hot-toast';
import useLatest from '@/hooks/useLatest';
import { setMaterialList } from '@/store/game';
import { IBagPetList } from '@/constant/type';

const StyledTitle = styled.div`
  width: 19.875rem;
  height: 4rem;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 1.5rem;
  top: 0;
  transform: translate(0%, -60%);
  & > img {
    position: absolute;
    top: 0;
    display: block;
    left: 0;
    width: 19.875rem;
    height: 4rem;
  }
  & > p {
    z-index: 1;
    color: #fff;
    margin: 0;
    text-align: center;
    text-shadow: 0.0625rem 0.125rem 0px #000b22;
    font-family: 'Baloo 2';
    font-size: 2.5rem;
    font-style: normal;
    font-weight: 800;

    line-height: 100%;
    text-transform: capitalize;
    position: relative;

    &::before {
      content: attr(data-text);
      position: absolute;
      -webkit-text-stroke: 0.25rem #4b2800;
      z-index: -1;
      left: 0;
    }
  }
`;

const StyledClose = styled(Close)`
  top: 0;
  transform: translate(0%, -60%);
`;

interface IPetFeedModalProps {
  portalContainerRef: RefObject<HTMLDivElement>;
}

const PetFeedModal = ({ portalContainerRef }: IPetFeedModalProps) => {
  const isPetFeedModalOpen = usePetPanelContextSelector((state) => state.isPetFeedModalOpen);
  const petFoodConfigList = usePetPanelContextSelector((state) => state.petFoodConfigList);
  const selectedPetId = usePetPanelContextSelector((state) => state.selectedPetId);
  const selectedPetInfo = usePetPanelContextSelector((state) => state.selectedPetInfo);

  const contextDispatch = usePetPanelContextDispatch();
  const materialList = useAppSelector((state) => state.GameReducer.materialList);
  const materialListRef = useRef<MaterialListRef>(null);
  const slotListRef = useRef<SlotListRef>(null);
  const [btnLoading, setBtnLoading] = useState(false);
  const currentSelectedItems = useRef<Array<{ item: any; value: number; userItemId: string }>>([]);
  const [previewStamina, setPreviewStamina] = useState(0);
  const petId = usePetPanelContextSelector((state) => state.selectedPetId);
  const { updateGameStatePetItemData } = useUpdatePetListItem();
  const dispatch = useAppDispatch();

  const latestSelectedPetInfoRef = useLatest(selectedPetInfo);

  const latestMaterialListRef = useLatest(materialList);

  const foodPreferenceSrc = useMemo(() => {
    let src = '';
    if (selectedPetInfo?.foodPreference) {
      ItemConfig.getInstance().getData(Number(selectedPetInfo.foodPreference), (data) => {
        if (data) {
          src = getCdnLink(data?.icon_url || '');
        }
      });
    }
    return src;
  }, [selectedPetInfo]);

  const foodList = useMemo(() => {
    const petFoodTags = petFoodConfigList.map((item) => item.tag);
    return materialList
      .filter((item) => petFoodTags.includes(item.tag))
      .map((item) => {
        const foodConfig = petFoodConfigList.find((food) => food.tag === item.tag);
        const preferFood = selectedPetInfo?.foodPreference;
        let stamina = foodConfig?.stamina;
        if (preferFood && foodConfig?.tag === preferFood) {
          stamina = (stamina || 0) * 2;
        }
        return { ...item, stamina: stamina };
      })
      .sort((a, b) => (a.stamina || 0) - (b.stamina || 0));
  }, [petFoodConfigList, materialList, selectedPetInfo]);

  const onCloseModal = () => {
    setBtnLoading(false);
    setPreviewStamina(0);
    contextDispatch({
      type: 'UPDATE',
      payload: {
        isPetFeedModalOpen: false,
      },
    });
  };

  useEffect(() => {
    setBtnLoading(false);
    setPreviewStamina(0);
  }, [isPetFeedModalOpen]);

  useEffect(() => {
    if (!selectedPetId) {
      onCloseModal();
    }
  }, [selectedPetId]);

  const onSelectedItemsChange = (
    selectedItems: Array<{ item: any; value: number; userItemId: string }>
  ) => {
    slotListRef.current?.setSelectedItems(selectedItems);
    currentSelectedItems.current = selectedItems;
    const totalStamina = selectedItems.reduce((total, cur) => {
      const curStamina = cur.item.stamina;
      const curTotal = cur.value * curStamina;
      return total + curTotal;
    }, 0);
    const staminaLimit =
      (latestSelectedPetInfoRef.current?.stamina || 0) -
      (latestSelectedPetInfoRef.current?.currentStamina || 0);

    setPreviewStamina(Math.min(totalStamina, staminaLimit));
  };

  const maxBtnDisabled = useMemo(() => {
    if (!selectedPetInfo) return false;
    if (selectedPetInfo.stamina)
      return previewStamina + (selectedPetInfo?.currentStamina || 0) >= selectedPetInfo.stamina;
  }, [previewStamina, selectedPetInfo]);

  const plusBtnDisabled = useMemo(() => {
    if (!selectedPetInfo) return false;
    if (selectedPetInfo.stamina)
      return previewStamina + (selectedPetInfo?.currentStamina || 0) >= selectedPetInfo.stamina;
  }, [previewStamina, selectedPetInfo]);

  const debounceHandleSelectedItemsChange = useMemo(() => debounce(onSelectedItemsChange, 100), []);

  useEffect(() => {
    return () => {
      debounceHandleSelectedItemsChange.cancel();
    };
  }, []);

  const onRemove = (userItemId: string) => {
    materialListRef.current?.removeItem(userItemId);
  };

  const updateBagMaterialList = (
    selectedItems: {
      item: any;
      value: number;
      userItemId: string;
    }[]
  ) => {
    if (selectedItems.length === 0) return;
    const latestMaterialList = latestMaterialListRef.current;
    const newList = [...latestMaterialList]
      .map((item) => {
        const selectItem = selectedItems.find((selectedItem) => selectedItem.item.tag === item.tag);

        if (selectItem) {
          const quantity = item.quantity - selectItem.value;
          return { ...item, quantity: quantity };
        } else {
          return { ...item };
        }
      })
      .filter((item) => item.quantity);
    dispatch(setMaterialList(newList));
  };

  const onSubmit = async () => {
    if (btnLoading) return;
    const selectedItems = currentSelectedItems.current;
    const data = {
      petId: petId,
      foodList: selectedItems.map((item) => ({ tag: item.item.tag, quantity: item.value })),
    };
    try {
      setBtnLoading(true);
      const res = await petFeed(data);
      if (res.data.code === 1) {
        const respData = res.data.data;
        updateBagMaterialList(currentSelectedItems.current);
        updateGameStatePetItemData({
          targetPet: { _id: respData.petId, currentStamina: respData.currentStamina },
        });
        materialListRef.current?.resetAllInputValue();
        currentSelectedItems.current = [];
        toast.success('Feed Success!');
        setBtnLoading(false);
        onCloseModal();
      } else {
        setBtnLoading(false);
        toast.error(res.data.msg);
      }
    } catch (error) {
      setBtnLoading(false);
    }
  };

  const petFeedConfig = useMemo(() => {
    return {
      showPreferFoodIcon: true,
      preferFoodTagList: selectedPetInfo?.foodPreference ? [selectedPetInfo.foodPreference] : [],
      staminaLimit: selectedPetInfo?.stamina || 0,
      previewStamina: previewStamina,
      currentStamina: selectedPetInfo?.currentStamina || 0,
    };
  }, [selectedPetInfo?.currentStamina, previewStamina, selectedPetInfo]);

  return (
    <Dialog
      isOpen={isPetFeedModalOpen}
      onClose={onCloseModal}
      closeOnOverlayClick={false}
      portalContainer={portalContainerRef?.current as HTMLElement}
      zIndex={13}>
      <ModalContent>
        <StyledTitle>
          <Image
            src="/image/buy-bg.png"
            alt="buy-tools"
            width={360}
            height={64}
            draggable={false}
          />
          <p data-text={'Feed'}>Feed</p>
        </StyledTitle>
        <StyledClose onClick={onCloseModal} />
        <div className="content">
          <div className="content-left">
            <MateriaList
              ref={materialListRef}
              materialList={foodList}
              onSelectedItemsChange={debounceHandleSelectedItemsChange}
              maxBtnDisabled={maxBtnDisabled}
              plusBtnDisabled={plusBtnDisabled}
              petFeedConfig={petFeedConfig}
            />
          </div>
          <StyledRightContent className="content-right">
            <StyledPetInfoSectionMain
              displayOnly
              spRecoverView={{
                previewStamina: previewStamina,
              }}
            />

            <StyledPetInfoItem>
              <PetInfoItemLabel>Favorite food:</PetInfoItemLabel>
              <PetInfoItemValue>
                {foodPreferenceSrc ? (
                  <StyledFooter>
                    <Image
                      src={foodPreferenceSrc}
                      width={28}
                      height={19}
                      alt="food prefer"
                      draggable={false}
                    />
                  </StyledFooter>
                ) : (
                  selectedPetInfo?.foodPreference
                )}
              </PetInfoItemValue>
            </StyledPetInfoItem>

            <div className="submit-resources">
              <span className="title">Feeding List</span>
              <span className="line" />
            </div>
            <div className="submit-resources-buttons">
              <button
                className="all-materials"
                onClick={() => {
                  materialListRef.current?.autoMaxFeed();
                }}>
                All Materials
              </button>
              <button
                className="clear-all"
                onClick={() => {
                  materialListRef.current?.resetAllInputValue();
                }}>
                Clear All
              </button>
            </div>
            <div className="slot-list">
              <SlotList materialList={foodList} onRemove={onRemove} ref={slotListRef} />
            </div>

            <div className="submit-buttons">
              <StyledButton
                className="cancel"
                onClick={() => {
                  onCloseModal();
                }}>
                Cancel
              </StyledButton>
              <StyledButton
                className="confirm"
                onClick={() => {
                  onSubmit();
                }}
                disabled={btnLoading || currentSelectedItems.current?.length === 0}>
                {btnLoading ? 'Feeding...' : 'Feed'}
              </StyledButton>
            </div>
          </StyledRightContent>
        </div>
      </ModalContent>
    </Dialog>
  );
};

const StyledPetInfoItem = styled(PetInfoItem)`
  width: 100%;
  box-sizing: border-box;
  background-color: #f7e7cd;
  margin-bottom: auto;
`;

const StyledPetInfoSectionMain = styled(PetInfoSectionMain)`
  padding: 1rem;
`;

const StyledButton = styled(Button)`
  height: 3.75rem;
  box-sizing: border-box;
`;

const StyledRightContent = styled.div`
  justify-content: flex-end;
  gap: 1rem !important;
  .submit-resources {
    color: #542d00;
    font-family: 'JetBrains Mono';
    font-size: 1rem;
    font-style: normal;
    font-weight: 700;
    text-transform: capitalize;
  }
`;

const DialogBody = styled(StyledDialogBody)`
  height: 40rem;
  width: 40rem;
  padding-right: 1rem;
`;

const StyledMateriaListWrapper = styled.div`
  height: 100%;
  overflow: overlay;
  padding-right: 0.5rem;
`;

export default PetFeedModal;
