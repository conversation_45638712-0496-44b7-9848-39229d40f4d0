import { ChatTabType } from '@/game/TS/Chat/ChatType';
import {
  ChannelIcon,
  ChannelMenuContainer,
  ChannelMenuItem,
  ChannelMenuItemWrapper,
  ChannelMenuWrapper,
  ChannelName,
  ChannelSelectArrowWrapper,
  ChannelTgIcon,
  ScrollBox,
} from './styles';
import { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { SpriteSvg } from '@/components/SvgWrapper';
import { debounce } from 'lodash';
import { ChatManager } from '@/game/TS/Chat/ChatManager';
import { ChatListener } from '@/game/TS/Chat/ChatListener';
import { ChatEvent } from '@/game/TS/Chat/ChatEvent';
import { useChatRoomContextDispatch, useChatRoomContextSelector } from '../../context';

const initialList = [
  {
    label: 'Global',
    icon: '/image/chatRoom/global-black.webp',
    withTgIcon: false,
    type: ChatTabType.Room,
  },
  {
    label: 'Satworld',
    icon: '/image/chatRoom/satworld2.png',
    type: ChatTabType.SatWorld,
    withTgIcon: true,

    link: 'https://t.me/UniWorldsHQ',
  },
  {
    label: 'Fractal',
    icon: '/image/chatRoom/fractal1.png',
    type: ChatTabType.Fractal,
    withTgIcon: true,
    link: 'https://t.me/fractal_bitcoin_official',
  },
  {
    label: 'WangCai',
    icon: '/image/chatRoom/wangcai.png',
    type: ChatTabType.WangCai,
    withTgIcon: true,
    link: 'https://t.me/wangcai_fractal',
  },
];

const ChannelSelectedArrow = ({ activeIndex = 0 }: { activeIndex?: number }) => {
  return (
    <ChannelSelectArrowWrapper $activeIndex={activeIndex}>
      <SpriteSvg id="channelArrow" />
    </ChannelSelectArrowWrapper>
  );
};

interface ChannelMenuProps {
  [k: string]: any;
  scrollToBottom?: () => void;
}

const ChannelMenu: React.FC<ChannelMenuProps> = ({ scrollToBottom = () => false }) => {
  const scrollBoxRef = useRef<HTMLDivElement>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const scrollEmptyBoxRef = useRef<HTMLDivElement>(null);
  const channelMenuRef = useRef<HTMLDivElement>(null);

  const contextDispatch = useChatRoomContextDispatch();
  const typeList = useChatRoomContextSelector((state) => state.typeList);
  const selectedTab = useChatRoomContextSelector((state) => state.selectedTab);

  const handleLeftTabScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    scrollBoxRef.current?.scrollTo(0, e.currentTarget.scrollTop);
  }, []);

  const handleChangeTab = (type: ChatTabType) => {
    contextDispatch({
      type: 'UPDATE',
      payload: { selectedTab: type, showNewMessageButton: false, isLockedScroll: false },
    });
    scrollToBottom?.();
  };

  const initUpdateUI = () => {
    if (scrollBoxRef.current?.clientHeight !== wrapperRef.current?.clientHeight) {
      scrollBoxRef.current?.style.setProperty(
        'height',
        (wrapperRef.current?.clientHeight ?? 0) + 'px'
      );
    }
    if (scrollEmptyBoxRef.current?.clientHeight !== channelMenuRef.current?.clientHeight) {
      scrollEmptyBoxRef.current?.style.setProperty(
        'height',
        (channelMenuRef.current?.clientHeight ?? 0) + 'px'
      );
    }
  };

  const [newMessageMap, setNewMessageMap] = useState<Map<ChatTabType, number>>(new Map());

  useEffect(() => {
    const updateMessageMap = () => {
      typeList?.forEach((type) => {
        const chatList = ChatManager.getInstance().getChatList(type);
        if (chatList) {
          setNewMessageMap((prev) => {
            prev.set(type, chatList.getNewMessageCount());
            return new Map(prev);
          });
        }
      });
    };
    updateMessageMap();
    ChatListener.getInstance().addListener(ChatEvent.NewMessageChange, updateMessageMap);
    return () => {
      ChatListener.getInstance().removeListener(ChatEvent.NewMessageChange, updateMessageMap);
    };
  }, [typeList]);

  const channelList = useMemo(() => {
    const arr = initialList
      .filter((item) => typeList?.includes(item.type))
      .map((item) => {
        return {
          ...item,
          active: item.type === selectedTab,
          withNew: (newMessageMap.get(item.type) || 0) > 0,
        };
      });

    return arr;
  }, [newMessageMap, selectedTab, typeList]);

  useLayoutEffect(() => {
    const haveNewMessage = channelList.some((item) => item.withNew);
    contextDispatch({ type: 'UPDATE', payload: { haveNewMessage: haveNewMessage } });
  }, [channelList]);

  useEffect(() => {
    initUpdateUI();

    const handleResize = debounce(() => {
      initUpdateUI();
    }, 500);

    const resizeObserver = new ResizeObserver(handleResize);
    if (wrapperRef.current) {
      resizeObserver.observe(wrapperRef.current);
    }
    return () => {
      resizeObserver.disconnect();
      handleResize.cancel();
    };
  }, []);

  const activeIndex = useMemo(() => {
    return channelList.findIndex((item) => item.type === selectedTab);
  }, [channelList, selectedTab]);

  return (
    <>
      <ChannelMenuWrapper ref={wrapperRef} onScroll={handleLeftTabScroll}>
        <ChannelMenuContainer ref={channelMenuRef}>
          {channelList.map((item) => {
            return (
              <ChannelMenuItemWrapper key={item.type} $active={item.type === selectedTab}>
                <ChannelMenuItem
                  onClick={() => {
                    handleChangeTab(item.type);
                  }}
                  $active={item.active}
                  $withNew={item.withNew}
                  onDoubleClick={() => {
                    if (item.link) {
                      window.open(item.link, '_blank');
                    }
                  }}>
                  <ChannelIcon $bgSrc={item.icon}>
                    {item.withTgIcon && <ChannelTgIcon />}
                  </ChannelIcon>
                  <ChannelName>{item.label}</ChannelName>
                </ChannelMenuItem>
              </ChannelMenuItemWrapper>
            );
          })}
        </ChannelMenuContainer>
      </ChannelMenuWrapper>

      <ScrollBox ref={scrollBoxRef}>
        <div ref={scrollEmptyBoxRef} />
        <ChannelSelectedArrow activeIndex={activeIndex} />
      </ScrollBox>
    </>
  );
};

ChannelMenu.displayName = 'ChannelMenu';

export default ChannelMenu;
