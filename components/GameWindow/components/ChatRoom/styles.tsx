import styled, { css } from 'styled-components';
import { motion } from 'framer-motion';
import SvgWrapper, { SpriteSvg } from '@/components/SvgWrapper';
export const ChatRoomContainer = styled(motion.div)`
  position: fixed;
  z-index: 999;
  width: 53.8125rem;
  height: 100vh;
  flex-shrink: 0;
  border-radius: 0rem 5rem 5rem 0rem;
  padding: 4.3125rem 2.3125rem 2.9375rem 1.875rem;
  background: rgba(255, 255, 255, 0.85);
  display: flex;
  flex-wrap: nowrap;
  box-sizing: border-box;
  * {
    box-sizing: border-box;
  }
`;

export const LeftTabContainer = styled.div`
  width: 14rem;
  height: auto;
  padding-right: 1rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  position: relative;
`;

export const RightBox = styled.div`
  display: flex;
  width: 35.875rem;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
  display: flex;
  flex-direction: column;
`;

export const MainMessageContainer = styled.div`
  flex: 1;
  display: flex;
  padding: 1rem;
  align-items: flex-start;
  gap: 0.5rem;
  align-self: stretch;
  border-radius: 2.5rem;
  border: 0.0625rem solid #cabfab;
  background: #fff;
  overflow-y: hidden;
  position: relative;
`;

export const ChatRoomPreviewContainer = styled.div<{ $isMobile?: boolean }>`
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: nowrap;
  gap: 1rem;
  height: 10rem;
  width: auto;
  flex-wrap: nowrap;
  box-sizing: border-box;
  position: fixed;
  left: 4rem;
  bottom: 2rem;
  z-index: 11;
  * {
    box-sizing: border-box;
  }

  ${({ $isMobile }) =>
    $isMobile
      ? css`
          left: 3.3333333333%;
          bottom: 3.7215189873%;
        `
      : css`
          left: 2.08333333333%;
          bottom: 2.962962963%;
        `}
`;
export const StyledSvgWrapper = styled(SvgWrapper)<{ $expand: boolean }>`
  position: absolute;
  display: flex;
  width: 3rem;
  height: 3rem;
  flex-shrink: 0;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  cursor: pointer;
  transform-origin: center center;
  right: 2.3125rem;
  top: 2.8125rem;
  ${({ $expand }) =>
    $expand
      ? css`
          transform: rotate(0deg);
          transition: transform 0.2s linear 0.2s;
        `
      : css`
          transform: rotate(180deg);
          transition: transform 0.2s linear;
        `}
  svg {
    filter: drop-shadow(-0.5rem 0px 0px #d74304);
  }
`;

export const PlayerIdBox = styled.span<{
  $isMe?: boolean;
  $isPrimary?: boolean;
  $withGMCopyStyle?: boolean;
}>`
  color: #4a3b28;
  font-family: Inter;
  font-size: 1.125rem;
  font-style: normal;
  font-weight: 600;
  line-height: 100%;
  white-space: nowrap;

  ${({ $isMe = false, $isPrimary = true }) =>
    !$isPrimary
      ? $isMe
        ? css`
            font-size: 1rem;
            color: #ffff27;
            font-weight: 700;
          `
        : css`
            font-size: 1rem;
            color: #c0c0c0;
            font-weight: 700;
          `
      : css``}

  ${({ $withGMCopyStyle = false }) =>
    $withGMCopyStyle &&
    css`
      cursor: pointer;
      &:hover {
        text-decoration: underline;
        text-decoration-style: dotted;
      }
    `}
`;

export const PlayerTag = styled.span<{
  $isMe: boolean;
  $isGM: boolean;
  $isAM: boolean;
  $isPrimary?: boolean;
}>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 1.25rem;
  gap: 0.625rem;
  border-radius: 3.125rem;
  border: 0.0625rem solid #140f08;
  box-shadow: 0rem 0.125rem 0rem 0rem #140f08;

  color: #fff;
  font-family: Inter;
  font-size: 0.75rem;
  font-style: normal;
  font-weight: 900;

  position: relative;
  z-index: 1;

  padding: 0.5rem;

  ${({ $isMe }) =>
    $isMe &&
    css`
      background: #16a6ff;
    `}
  ${({ $isAM }) =>
    $isAM &&
    css`
      background: #ffc945;
    `}
  ${({ $isGM }) =>
    $isGM &&
    css`
      background: #ff8316;
    `}

  ${({ $isPrimary }) =>
    !$isPrimary &&
    css`
      height: 1rem;
      font-size: 0.6rem;
      border: 0.05rem solid #140f08;
      box-shadow: 0rem 0.1rem 0rem 0rem #140f08;
    `}

  &::before {
    content: attr(data-text);
    position: absolute;
    display: block;
    -webkit-text-stroke: 0.1375rem #140f08;
    z-index: -1;
  }
`;

export const MessageItemReplyBox = styled.p`
  margin: 0;
  display: inline-flex;
  align-items: center;
  flex-wrap: nowrap;
  gap: 0.25rem;
  border-radius: 1rem;
  background: #cabfab;
  height: 1.75rem;
  padding: 0px 0.5rem;
`;

export const ReplyContent = styled.span`
  overflow: hidden;
  color: #fff;
  text-overflow: ellipsis;
  font-family: Inter;
  font-size: 1rem;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  word-break: break-all;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
`;

export const ReplyPlayerIdBox = styled(PlayerIdBox)`
  color: #fff;
  font-family: Inter;
  font-size: 1rem;
  font-weight: 600;
  white-space: nowrap;
`;

const ReplyIconWrapper = styled(SvgWrapper)`
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
`;

export const ReplyIcon = () => {
  return (
    <ReplyIconWrapper>
      <SpriteSvg id="replayIcon" />
    </ReplyIconWrapper>
  );
};

export const ReplyBoxContainer = styled(motion.div)`
  display: flex;
  height: 2rem;
  padding: 0rem 1rem;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  border-radius: 1.5rem;
  background: #fff;
  gap: 0.5rem;
  flex-wrap: nowrap;
`;
