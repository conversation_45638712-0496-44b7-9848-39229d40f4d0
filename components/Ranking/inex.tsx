import { useEffect, useMemo, useRef, useState } from 'react';
import RankingModal from './components/Modal';
import { RankingTableBtnView, RankingTableContainer } from './style';
import Card from './components/Card';
import { useDispatch, useSelector } from 'react-redux';
import { IAppState, SCENE_TYPE } from '@/constant/type';
import { TreeBar } from './components/TreeBarList';
import { getDaysAndHours, getRemainingTime } from '@/utils/activities';
import { useRank } from '@/hooks/useRank';
import GlobalSpaceEvent, { CharacterType, GlobalDataKey } from '@/game/Global/GlobalSpaceEvent';
import * as THREE from 'three';
import { NpcConfig } from '@/game/Config/NpcConfig';
import { useRouter } from 'next/router';
// import JinBiMAnimate from "./components/JinbiMAnimate";
import { setLeaderboard, setMenuType } from '@/store/app';
import { RankingConfig } from '@/game/Config/RankingConfig';
import { TabItem } from './components/Tabs';
import { MenuType } from './components/Menu';
import TreeBarCarousel from './components/TreeBarCarousel';
import rank1 from '/public/image/rank1.png'; // wangcai社区头像
import rank2 from '/public/image/rank2.png'; // potato社区头像
import rank3 from '/public/image/rank3-1.png'; // TheLonelyBit社区头像
import rank4 from '/public/image/rank4.png'; // Pizza社区头像
import rank5 from '/public/image/rank5.png'; // DomoDucks社区头像
import TimerGroup from './components/TimerGroup';
import { StoneConfig } from '@/game/Config/StoneConfig';
import { TreeConfig } from '@/game/Config/TreeConfig';
import { useResourceList } from '@/hooks/useResourceList';
import { AppGameApiKey, GetMyPlayer } from '@/game/TSX/Character/MyPlayer';
import { LoadingPageType } from '@/game/Config/DoorConfig';
import { ConfigManager } from '@/game/Config/ConfigManager';
import PetBetaTestTipEnter from './components/PetBetaTestTipEnter';
import { IS_RELEASE_ONLINE } from '@/constant';
import PetTestEnter from '../Header/components/PetTestEnter';

interface MenuItem {
  name: string;
  active: boolean;
  tabs: {
    type: string;
    start_time: number;
    end_time: number;
  }[];
}

interface TabData {
  type: string;
  start_time: number;
  end_time: number;
}

function Ranking() {
  const [isOpen, setIsOpen] = useState(false);
  // const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const dispatch = useDispatch();
  const prevMenuTypeRef = useRef('menu-0');
  const {
    userBasicInfo,
    btcAddress,
    menuType,
    leaderboard,
    rockLeftTime,
    treeLeftTime,
    sceneType,
  } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const [currentMenu, setCurrentMenu] = useState<string>('menu-0'); // 默认显示menu-0
  const [menus, setMenus] = useState<MenuItem[]>([]);
  const [currentMenuTabs, setCurrentMenuTabs] = useState<TabItem[]>([]);
  const [selectedTabIndex, setSelectedTabIndex] = useState(0);
  const [currentTab, setCurrentTab] = useState<TabItem | null>(null);
  const [currentTabStatus, setCurrentTabStatus] = useState('');
  const myPlayer = GetMyPlayer();
  const refreshTimeStamp = myPlayer.refreshTimeStamp;
  // 使用 state 来跟踪是否应该显示倒计时
  // const [shouldShowCooldown, setShouldShowCooldown] = useState(false);
  const [shouldShowRockCooldown, setShouldShowRockCooldown] = useState(false);
  const [shouldShowTreeCooldown, setShouldShowTreeCooldown] = useState(false);
  // 使用 ref 来跟踪是否是由 onTimeEnd 触发的刷新
  // const isRefreshingFromTimeout = useRef(false);
  const isRockRefreshingFromTimeout = useRef(false);
  const isTreeRefreshingFromTimeout = useRef(false);

  const communityDataRef = useRef<any>(null);

  const { refreshRockList, refreshTreeList } = useResourceList({
    autoFetch: false,
  });

  const basicRankInfos = useMemo(() => {
    return userBasicInfo?.rankInfos || [];
  }, [userBasicInfo]);

  const {
    fishRankList,
    getRankingListByType,
    loading,
    rankListByType,
    getRankingListByType2,
    getBatchRankingListByType,
  } = useRank({
    initialFetch: false,
    manualPolling: false,
  });

  const jumpActivityNpc = () => {
    NpcConfig.getInstance().getData(2001, (data) => {
      const transformPos = new THREE.Vector3(
        data.transformPosition[0],
        data.transformPosition[1] + 1,
        data.transformPosition[2]
      );
      const direction = transformPos
        .clone()
        .sub(new THREE.Vector3(data.position[0], data.position[1] + 1, data.position[2]));

      myPlayer.callAppApi(AppGameApiKey.setLoaderType, LoadingPageType.Default);
      GlobalSpaceEvent.SetDataValue(GlobalDataKey.TransformData, {
        characterType: CharacterType.Player,
        position: transformPos,
        sceneType: data.transformMapId as SCENE_TYPE,
        camDirection: direction,
      });
    });
  };

  // 处理标签页数据，计算状态
  const processTabsData = (tabs: TabData[]): TabItem[] => {
    const currentTime = Date.now();

    return tabs.map((tab, index) => {
      let status: 'upcoming' | 'ongoing' | 'expired' = 'upcoming';

      if (currentTime < tab.start_time) {
        status = 'upcoming'; // 未开始
      } else if (currentTime < tab.end_time) {
        status = 'ongoing'; // 进行中
      } else {
        status = 'expired'; // 已结束
      }

      return {
        name: `Event ${index + 1}`,
        type: tab.type,
        status: status,
      };
    });
  };

  // 当菜单变化时更新标签页
  useEffect(() => {
    if (menus.length === 0) return;

    if (!isOpen) return;

    const selectedMenu = menus.find((menu) => menu.name === currentMenu);
    if (selectedMenu && selectedMenu.tabs) {
      const processedTabs = processTabsData(selectedMenu.tabs);
      setCurrentMenuTabs(processedTabs);

      // 优先选择进行中的标签，否则选择第一个
      const ongoingIndex = processedTabs.findIndex((tab) => tab.status === 'ongoing');
      const initialIndex = ongoingIndex !== -1 ? ongoingIndex : 0;
      setSelectedTabIndex(initialIndex);
      const currentTab = processedTabs[initialIndex];
      setCurrentTab(currentTab);
      setCurrentTabStatus(currentTab.status);
      getRankingListByType(currentTab.type);
    }
  }, [currentMenu, menus, isOpen]);

  // 活动状态判断 - 现在基于当前选中的菜单和标签
  const activityStatus = useMemo(() => {
    // 根据当前标签的状态直接映射到活动状态
    switch (currentTabStatus) {
      case 'upcoming':
        return 'upcoming'; // 活动即将开始
      case 'expired':
        return 'ended'; // 活动已结束
      case 'ongoing':
        return 'ongoing'; // 活动进行中
      default:
        return 'none';
    }
  }, [currentTabStatus]);

  // 获取当前标签的开始和结束时间
  const getCurrentTabTimes = useMemo(() => {
    if (!currentMenu || !currentTab || !menus.length) return { start_time: 0, end_time: 0 };

    // 查找当前菜单
    const menu = menus.find((m) => m.name === currentMenu);
    if (!menu || !menu.tabs) return { start_time: 0, end_time: 0 };

    // 查找当前标签
    const tab = menu.tabs.find((t) => t.type === currentTab.type);
    if (!tab) return { start_time: 0, end_time: 0 };

    return {
      start_time: tab.start_time,
      end_time: tab.end_time,
    };
  }, [currentMenu, currentTab, menus]);

  // 根据当前选中tab的状态决定显示的数据
  const getRankItems = useMemo(() => {
    if (!rankListByType) {
      return [];
    }

    // 所有菜单类型都返回相同的数据结构，简化逻辑
    return rankListByType?.rankInfo || [];
  }, [rankListByType]);

  // 同样处理自身排名信息
  const getSelfRankInfo = useMemo(() => {
    if (!rankListByType || menuType === 'menu-0') {
      return null;
    }
    const result = {
      address: btcAddress,
      rank: rankListByType.rank <= 0 ? 0 : rankListByType.rank,
      score: rankListByType.score <= 0 ? 0 : rankListByType.score,
    };

    return result;
  }, [menuType, rankListByType]);

  // 设置默认菜单和初始标签
  useEffect(() => {
    // if (!isOpen) return;
    RankingConfig.getInstance().getData((data) => {
      if (!data.menus || data.menus.length === 0) return;
      setMenus(data.menus);
    });
  }, []);

  const handleMenuClick = (menuType: MenuType) => {
    setCurrentMenu(menuType);
    dispatch(setMenuType(menuType));
  };

  // 添加根据菜单类型获取活动标签剩余时间的函数
  const getActiveTabTimeByMenuType = (menuType: string) => {
    if (!menus.length) return '';

    const menu = menus.find((m) => m.name === menuType);
    if (!menu || !menu.tabs) return '';

    // 找到该菜单中的活跃标签
    const activeTab = menu.tabs.find((tab) => {
      const currentTime = Date.now();
      return currentTime >= tab.start_time && currentTime < tab.end_time;
    });

    if (!activeTab) return '';

    // 使用格式化函数来显示剩余时间
    const remainingMs = getRemainingTime(activeTab);
    if (remainingMs <= 0) return '';

    // 使用已有的工具函数格式化时间
    return getDaysAndHours(Date.now() + remainingMs);
  };

  // 获取当前menu-0的type
  const getCurrentMenu0Type = () => {
    let type = '';
    ConfigManager.getInstance().getData((data) => {
      const menus = data.menus;
      const menu = menus.find((item) => item.name === 'menu-0');
      if (menu) {
        const tabs = menu.tabs;
        type = tabs[0].type;
      }
    });
    return type;
  };

  // 定期获取社区数据
  const getCommunityDataList = async () => {
    try {
      // 查找当前活跃的标签类型
      const activityTags = {
        Community: getCurrentMenu0Type(),
        Wood: '',
        Stone: '',
        Fish: '',
      };

      // 动态获取当前活跃的标签类型
      if (menus && menus.length > 0) {
        menus.forEach((menu) => {
          if (!menu.tabs || menu.tabs.length === 0) return;

          // 查找当前活跃的标签
          const currentTime = Date.now();
          const activeTab = menu.tabs.find(
            (tab) => currentTime >= tab.start_time && currentTime < tab.end_time
          );

          if (!activeTab) return;

          // 根据标签类型设置对应的活动标签
          switch (menu.name) {
            case 'menu-0':
              activityTags.Community = getCurrentMenu0Type();
              break;
            case 'menu-1':
              activityTags.Wood = activeTab.type;
              break;
            case 'menu-2':
              activityTags.Stone = activeTab.type;
              break;
            case 'menu-3':
              activityTags.Fish = activeTab.type;
              break;
            default:
              break;
          }
        });
      }

      // 创建一个存储所有活动数据的对象
      const allActivityData: any[] = [];

      const activityTagArr = Object.values(activityTags);
      const activityTagsEntries = Object.entries(activityTags);

      const tagString = activityTagArr.filter((item) => item).join(',');

      try {
        const batchData = await getBatchRankingListByType(tagString);

        batchData?.forEach((item) => {
          const { activityTag, data } = item;

          const rankType = activityTagsEntries.find((it) => it[1] === activityTag)?.[0];
          allActivityData.push({
            score: data.score,
            activityTag: activityTag,
            rank: data.rank,
            rankType: rankType,
            rankInfo: data.rankInfo,
          });
        });
      } catch (error) {
        //
      }

      // 如果获取到了数据，更新到Redux存储
      if (allActivityData.length > 0) {
        dispatch(setLeaderboard(allActivityData));
      }
    } catch (error) {
      console.error('getAllActivityData error', error);
    }
  };

  // 添加定时器逻辑，在组件挂载时启动，在组件卸载时清除
  useEffect(() => {
    // 如果是在社区场景，则不获取数据
    // if (sceneType === SCENE_TYPE.Community) return;

    // 设置定时器，比如每1分钟获取一次数据
    const timerId = setInterval(
      () => {
        getCommunityDataList();
      },
      1 * 60 * 1000
    ); // 5分钟

    // 组件卸载时清除定时器
    return () => {
      clearInterval(timerId);
    };
  }, [menus]); // 空依赖数组，表示只在组件挂载和卸载时执行

  // 统一获取排行榜数据的函数，优先使用leaderboard（最新数据）
  const getRankData = (rankType: string) => {
    // 如果leaderboard中有数据，优先使用
    // console.log("leaderboard=====", leaderboard);
    if (leaderboard) {
      const leaderboardData = Object.values(leaderboard).find(
        (item: any) => item.rankType === rankType
      );
      if (leaderboardData) return leaderboardData;
    }

    // 否则使用初始数据
    if (basicRankInfos) {
      const basicData = basicRankInfos.find((item: any) => item.rankType === rankType);
      if (basicData) return basicData;
    }

    // 如果都没有，返回null
    return null;
  };

  // 处理社区数据的函数，现在使用统一的数据获取函数
  const getCommunityData = () => {
    const communityData = getRankData('Community');
    // console.log("communityData=====", communityData);
    if (!communityData?.rankInfo || communityData.rankInfo.length === 0) {
      return []; // 如果没有数据，返回空数组
    }

    // 处理排名数据（与之前相同）
    const rankInfoArray = [...communityData.rankInfo];
    rankInfoArray.sort((a, b) => (a.rank || 0) - (b.rank || 0));

    // 创建社区排名结果数组
    interface CommunityRankItem {
      address: string;
      icon: string;
      score: number;
      rank: number;
    }

    const result: (CommunityRankItem | undefined)[] = [];

    // 维持原有的排名顺序：索引0是第2名，索引1是第1名，索引2是第3名
    const rankPositions = [2, 1, 3, 4, 5]; // 按照显示顺序排列的排名

    // 遍历排名顺序
    rankPositions.forEach((rank, index) => {
      const communityPlace = rankInfoArray.find((item) => item.rank === rank);
      if (communityPlace) {
        let icon = '';
        // 根据社区名称分配图标
        switch (communityPlace.address) {
          case 'wangcai':
            icon = rank1.src;
            break;
          case 'potato':
            icon = rank2.src;
            break;
          case 'TheLonelyBit':
            icon = rank3.src;
            break;
          case 'Pizza':
            icon = rank4.src;
            break;
          case 'DomoDucks':
            icon = rank5.src;
            break;
          default:
            // 如果没有匹配的社区，设置一个默认图标
            icon = rank1.src;
        }

        result[index] = {
          address: communityPlace.address,
          icon: icon,
          score: communityPlace.score,
          rank: rank,
        };
      }
    });

    return result.filter((item): item is CommunityRankItem => item !== undefined);
  };

  // 计算社区数据
  const communityRankInfo = useMemo(() => {
    return getCommunityData();
  }, [leaderboard, basicRankInfos]); // 依赖于排名数据变化

  // console.log("communityRankInfo=====", communityRankInfo);

  // 计算其他活动的排名和分数
  const activityRanks = useMemo(() => {
    return {
      wood: {
        rank: getRankData('Wood')?.rank || '',
        score: getRankData('Wood')?.score || '',
      },
      stone: {
        rank: getRankData('Stone')?.rank || '',
        score: getRankData('Stone')?.score || '',
      },
      fish: {
        rank: getRankData('Fish')?.rank || '',
        score: getRankData('Fish')?.score || '',
      },
    };
  }, [leaderboard, basicRankInfos]); // 依赖于排名数据变化

  //  计算活动时间，这里暂时不添加定时更新逻辑
  const activityTimes = useMemo(() => {
    return {
      menu0: getActiveTabTimeByMenuType('menu-0'),
      menu1: getActiveTabTimeByMenuType('menu-1'),
      menu2: getActiveTabTimeByMenuType('menu-2'),
      menu3: getActiveTabTimeByMenuType('menu-3'),
    };
  }, [menus]); // 只依赖于菜单配置，暂不考虑时间更新

  const barMap = useMemo(
    () => [
      // 社区
      {
        bgImg: '/image/cm.png',
        type: 'menu-0',
        time: activityTimes.menu0,
        rankInfo: communityRankInfo,
        rankType: 'Community',
        icon: '/image/community-icon.png',
        rank: 0,
        score: 0,
      },
      // 砍树
      {
        bgImg: '/image/mu-tree.png',
        type: 'menu-1',
        icon: '/image/t2-1.png',
        rank: activityRanks.wood.rank,
        score: activityRanks.wood.score,
        time: activityTimes.menu1,
        rankType: 'Wood',
      },
      // 挖矿
      {
        bgImg: '/image/tree1.png',
        type: 'menu-2',
        icon: '/image/t2-2.png',
        rank: activityRanks.stone.rank,
        score: activityRanks.stone.score,
        time: activityTimes.menu2,
        rankType: 'Stone',
      },
      // 钓鱼
      {
        bgImg: '/image/rod-bg.png',
        type: 'menu-3',
        icon: '/image/t2-3.png',
        rank: activityRanks.fish.rank,
        score: activityRanks.fish.score,
        time: activityTimes.menu3,
        rankType: 'Fish',
      },
    ],
    [activityTimes, communityRankInfo, activityRanks]
  ); // 依赖于之前计算的缓存结果

  // 是否显示岩石冷却时间
  const isShowRockLeftTime = useMemo(() => {
    return rockLeftTime && rockLeftTime > 0 && btcAddress;
  }, [rockLeftTime, btcAddress]);

  // 是否显示树木冷却时间
  const isShowTreeLeftTime = useMemo(() => {
    return treeLeftTime && treeLeftTime > 0 && btcAddress;
  }, [treeLeftTime, btcAddress]);

  // 监听rockLeftTime变化
  useEffect(() => {
    if (rockLeftTime && rockLeftTime > 0) {
      if (!isRockRefreshingFromTimeout.current) {
        setShouldShowRockCooldown(true);
      }
    } else {
      setShouldShowRockCooldown(false);
    }
    isRockRefreshingFromTimeout.current = false;
  }, [rockLeftTime]);

  // 监听treeLeftTime变化
  useEffect(() => {
    if (treeLeftTime && treeLeftTime > 0) {
      if (!isTreeRefreshingFromTimeout.current) {
        setShouldShowTreeCooldown(true);
      }
    } else {
      setShouldShowTreeCooldown(false);
    }
    isTreeRefreshingFromTimeout.current = false;
  }, [treeLeftTime]);

  // 岩石倒计时结束处理
  const onRockTimeEnd = async () => {
    isRockRefreshingFromTimeout.current = true;
    setShouldShowRockCooldown(false);
    const remainingLivingStoneCount = StoneConfig.getInstance().getAliveStoneCount();
    if (remainingLivingStoneCount === 0) {
      await refreshRockList();
    }
  };

  // 树木倒计时结束处理
  const onTreeTimeEnd = async () => {
    isTreeRefreshingFromTimeout.current = true;
    setShouldShowTreeCooldown(false);
    const remainingLivingTreeCount = TreeConfig.getInstance().getAliveTreeCount();
    if (remainingLivingTreeCount === 0) {
      await refreshTreeList();
    }
  };

  // 判断当前router 如果是home 则不显示
  const isHome = router.pathname === '/home';
  if (isHome) {
    return <></>;
  }

  // 活动进行中，显示正常内容
  return (
    <div>
      {!btcAddress ? (
        <PetTestEnter />
      ) : (
        <div
          style={{
            position: 'relative',
          }}>
          {/* <TreeBarCarousel>
            {barMap.map((item) => (
              <TreeBar
                key={item.type}
                {...item}
                activityStatus={activityStatus}
                onClick={(type) => {
                  setCurrentMenu(type);
                  dispatch(setMenuType(type));

                  setIsOpen(true);
                }}
                rankList={fishRankList}
              />
            ))}
            {IS_RELEASE_ONLINE && <PetBetaTestTipEnter />}
          </TreeBarCarousel> */}
          <div>
            <PetTestEnter />
          </div>
          {/* 冷却期组件相关 */}
          <TimerGroup
            isShowRockLeftTime={!!isShowRockLeftTime}
            isShowTreeLeftTime={!!isShowTreeLeftTime}
            shouldShowRockCooldown={shouldShowRockCooldown}
            shouldShowTreeCooldown={shouldShowTreeCooldown}
            rockLeftTime={rockLeftTime || 0}
            treeLeftTime={treeLeftTime || 0}
            onRockTimeEnd={onRockTimeEnd}
            onTreeTimeEnd={onTreeTimeEnd}
          />
        </div>
      )}

      <RankingModal
        isOpen={isOpen}
        onClose={() => {
          setIsOpen(false);
          dispatch(setMenuType(''));
          prevMenuTypeRef.current = '';
          setSelectedTabIndex(0);
          setCurrentTabStatus('');
        }}
        loading={loading}
        endTimestamp={
          activityStatus === 'none' || activityStatus === 'ended' ? 0 : getCurrentTabTimes.end_time
        }
        communityEndTimestamp={getCurrentTabTimes.end_time}
        tabItems={currentMenuTabs}
        currentChangeTab={(tab) => {
          // console.log("tab======", tab);
          setCurrentTab(tab);
          setCurrentTabStatus(tab.status);
          getRankingListByType(tab.type);
        }}
        currentMenu={currentMenu}
        onMenuClick={handleMenuClick}
        initialActiveIndex={selectedTabIndex}
        communityRankList={getRankItems}>
        <Card
          topGradient="linear-gradient(to bottom, #fce0a4, transparent)"
          bottomGradient="linear-gradient(to top, #fce0a4, transparent)"
          padding="0px"
          showBottomGradient={false}
          items={getRankItems}
          selfRankInfo={getSelfRankInfo}
          activityStatus={activityStatus}
        />
      </RankingModal>
    </div>
  );
}

export default Ranking;
