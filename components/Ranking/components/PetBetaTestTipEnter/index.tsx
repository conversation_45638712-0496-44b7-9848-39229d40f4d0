import Dialog from '@/commons/Dialog';
import { useCallback } from 'react';
import styled from 'styled-components';
import BasicCommunityModalContent, { BasicTitle } from '@/components/BasicCommunityModalContent';
import { motion } from 'motion/react';
import { useAppDispatch, useAppSelector } from '@/hooks/useStore';
import { updateModalState } from '@/store/modal';

const PetBetaTestTipEnter = () => {
  const dispatch = useAppDispatch();
  const open = useCallback(() => {
    dispatch(updateModalState({ showBetaPetTestEnterModal: true }));
  }, []);

  return (
    <PetTestEnterContainer
      onClick={() => {
        open();
      }}>
      <StyledMainTitle data-text={'SatWorld Pets'}>SatWorld Pets</StyledMainTitle>
      <StyledSubTitle data-text={'(Alpha)'}>(Alpha)</StyledSubTitle>
      <PetTestDescModal />
    </PetTestEnterContainer>
  );
};

const text = `“SatWorld Pets” is a chain-integrated gameplay developed by SatWorld based on the Alkanes contract.
It is currently undergoing a data-wipe test on Signet.`;

const PetTestDescModal = () => {
  const showBetaPetTestEnterModal = useAppSelector(
    (state) => state.ModalReducer.showBetaPetTestEnterModal
  );
  const dispatch = useAppDispatch();

  const close = () => {
    dispatch(updateModalState({ showBetaPetTestEnterModal: false }));
  };

  const onConfirm = () => {
    location.href = 'https://exp.satworld.io/';
    close();
  };

  return (
    <Dialog isOpen={showBetaPetTestEnterModal} onClose={close}>
      <StyledBasicCommunityModalContent
        modalWidth="53.5rem"
        modalHeight="38.375rem"
        closeButton={false}
        headerStyle={{
          top: 0,
          left: 0,
          transform: 'translate(0, -50%)',
        }}
        onClose={close}
        title={<StyledBasicTitle title="SatWorld Pets(Alpha)"></StyledBasicTitle>}
        onCancel={close}
        onConfirm={onConfirm}
        confirmText="Join Test">
        <ContentContainer>
          <TextContainer>{text}</TextContainer>
          <MenuContent
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}>
            <List>
              <li>
                <p>
                  This is a data-wipe test. All data will be cleared and the will be discarded after
                  the test ends.
                </p>
              </li>
              <li>
                <p>Test scope includes:</p>
                <p>• Verification of smart contract functionality for the pet system </p>
                <p>• Gameplay and visual testing of in-game pet features</p>
              </li>
              <li>
                <p>
                  Players can click the button below to visit a third-party site and claim test
                  tokens.
                </p>
              </li>
              <li>
                <p>
                  All content is for testing purposes only and does not represent the final product
                  quality.
                </p>
              </li>
            </List>
          </MenuContent>
        </ContentContainer>
      </StyledBasicCommunityModalContent>
    </Dialog>
  );
};

const ContentContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  gap: 2rem;
`;

const TextContainer = styled.div`
  margin-right: auto;
  color: #140f08;
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 120%;
  letter-spacing: -0.048rem;
`;

const MenuContent = styled(motion.div)`
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 1.5rem;
  background-color: #f5e5c7;
  border-radius: 1.25rem;

  box-sizing: border-box;

  padding: 2rem 1.5rem;
  background: #f7e7cd;
  box-shadow: 0px 0px 0.5rem 0px rgba(0, 0, 0, 0.25) inset;
`;

const List = styled.ul`
  list-style: none;
  margin: 0;
  padding: 0 0 0 1rem;
  & > li {
    padding-left: 0.5rem;
    list-style-type: '*';
    color: #140f08;
    font-family: 'JetBrains Mono';
    font-size: 1.125rem;
    font-style: normal;
    font-weight: 400;
    line-height: 120%;
    letter-spacing: -0.045rem;
    &:not(:first-of-type) {
      margin-top: 1rem;
    }
    & > p {
      margin: 0;
      display: inline-flex;
      align-items: center;
      flex-wrap: wrap;
    }
  }
`;

const StyledBasicCommunityModalContent = styled(BasicCommunityModalContent)`
  padding: 4.5rem 4rem 2.5rem 4rem;
  border: none;
  box-shadow:
    inset 0 0 0 0.75rem #ff8316,
    0 0 0 0.25rem #140f08;
  border-radius: 3rem;
`;

const StyledBasicTitle = styled(BasicTitle)`
  width: 22.5rem;
  height: 4rem;
  & > img {
    width: 22.5rem;
    height: 4rem;
  }
  & > p {
    color: #fff;
    text-align: center;
    text-shadow: 0.046875rem 0.09375rem 0 #000b22;
    font-family: 'Baloo 2';
    font-size: 1.875rem;
    font-style: normal;
    font-weight: 800;
    line-height: 100%;
    position: relative;
    z-index: 1;
    text-transform: capitalize;
    &::before {
      content: attr(data-text);
      position: absolute;
      -webkit-text-stroke: 0.25rem #664830;
      z-index: -1;
      left: 0;
    }
  }
`;

const PetTestEnterContainer = styled.div`
  background-image: url('/image/petTestEnter.webp');
  background-size: 18rem 4rem;
  background-repeat: no-repeat;
  background-position: center;
  display: flex;
  width: 18rem;
  height: 4rem;
  padding: 0.1875rem 0 0.152125rem 0;
  justify-content: flex-start;
  align-items: center;
  box-sizing: border-box;
  flex-direction: column;
  cursor: pointer;
`;
const StyledMainTitle = styled.p`
  margin: 0;
  margin-top: 0.875rem;
  color: #fff;
  text-align: center;
  font-family: 'Baloo 2';
  font-size: 1.5rem;
  font-style: normal;
  font-weight: 800;
  line-height: 95%; /* 1.425rem */
  position: relative;
  text-transform: capitalize;
  z-index: 1;
  transform: translateX(0.625rem);
  &::before {
    content: attr(data-text);
    position: absolute;
    -webkit-text-stroke: 0.25rem #664830;
    z-index: -1;
    left: 0;
  }
`;
const StyledSubTitle = styled.p`
  margin: 0;
  color: #fff;
  text-align: center;
  font-family: 'Baloo 2';
  font-size: 1.125rem;
  font-style: normal;
  font-weight: 800;
  line-height: 95%;
  position: relative;
  text-transform: capitalize;
  z-index: 1;
  transform: translateX(0.625rem);

  &::before {
    content: attr(data-text);
    position: absolute;
    -webkit-text-stroke: 0.25rem #664830;
    z-index: -1;
    left: 0;
  }
`;

export default PetBetaTestTipEnter;
