import styled, { css } from 'styled-components';
import KeyBg from '/public/image/bag/key-bg.png';
import effectPng from '/public/image/effect.png';

export const ContentItemView = styled.div<{
  check?: boolean;
  disabled?: boolean;
  boxShadow?: string;
  effect?: boolean;
  isGlowWeapon?: boolean;
  currentDurability?: number;
}>`
  width: 7.5rem;
  height: 7.5rem;
  padding: 0.25rem;
  ${({ check }) =>
    check
      ? css`
          box-shadow:
            0 0 0 0.25rem #ff8316 inset,
            0rem 0.125rem 0.5rem 0rem #00000026;
        `
      : css`
          box-shadow:
            0 0 0 0.0625rem #cabfab inset,
            0rem 0.125rem 0.5rem 0rem #00000026;
        `}
  transition: box-shadow 0.1s ease-in;
  background: #fbf4e8;
  border-radius: 1.25rem;
  position: relative;
  cursor: ${({ disabled }) => (disabled ? 'not-allowed' : 'pointer')};
  filter: ${({ disabled }) => (disabled ? 'grayscale(100%)' : 'none')};
  box-sizing: border-box;
  /* effect如果为true，则使用伪类元素加载图片 */
  &::before {
    content: ${({ effect, isGlowWeapon }) => (effect || isGlowWeapon ? '""' : 'none')};
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url(${effectPng.src});
    background-size: 100% 100%;
    display: ${({ effect, isGlowWeapon }) => (effect || isGlowWeapon ? 'block' : 'none')};
    z-index: 6;
  }

  & > .content-image {
    width: 100%;
    height: 100%;
    border-radius: 1.25rem;
    position: absolute;
    padding: 0.25rem;
    box-sizing: border-box;
    z-index: 7;
    left: 0;
    top: 0;
  }
  & > .content-num {
    position: absolute;
    left: 1.25rem;
    bottom: 0.3125rem;
    font-family: 'JetBrains Mono';
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 0.875rem;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    z-index: 9;
    color: #ffffff;
    // 文字描边
    text-shadow:
      0.0625rem 0.0625rem 0 #a58061,
      -0.0625rem 0.0625rem 0 #a58061,
      0.0625rem -0.0625rem 0 #a58061,
      -0.0625rem -0.0625rem 0 #a58061;
  }
  & > .content-new {
    width: 3rem;
    height: 3rem;
    position: absolute;
    right: -0.5rem;
    top: -0.625rem;
    z-index: 8;
  }
  & > .quest-key {
    width: 3.5rem;
    height: 2.1875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: url('${KeyBg.src}');
    background-size: 100% 100%;
    z-index: 10;
    font-family: 'JetBrains Mono';
    font-size: 1.25rem;
    font-weight: 400;
    line-height: 1.25rem;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #ffffff;
    position: absolute;
    right: -0.75rem;
    top: -0.375rem;
    padding-bottom: 0.625rem;
    box-sizing: border-box;
  }
  & > .red-mark {
    width: 1rem;
    height: 1rem;
    background: #ff4516;
    border: 0.125rem solid #140f08;
    border-radius: 50%;
    box-sizing: border-box;
    position: absolute;
    left: -0.125rem;
    top: -0.125rem;
    box-shadow: 0rem 0.25rem 0.25rem 0rem #00000040;
  }
`;

export const TooltipBoxView = styled.div<{
  quality?: number;
}>`
  width: 26.875rem;
  height: auto;
  background: #ffffff;
  border: 0.25rem solid #140f08;
  box-shadow: 0rem 0.25rem 1rem 0rem #00000040;
  padding: 1.5rem;
  box-sizing: border-box;
  border-radius: 2rem;
  position: relative;

  .tooltip-box-title {
    color: ${({ quality }) => {
      switch (quality) {
        case 1:
          return '#140F08';
        case 2:
          return '#00C724';
        case 3:
          return '#00BBFF';
        case 4:
          return '#9279FF';
        case 5:
          return '#FFD900';
        case 6:
          return '#FF9500';
        case 7:
          return '#FF2727';
        default:
          return '#140F08';
      }
    }};
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 1.75rem;
    border: 0.125rem solid
      ${({ quality }) => {
        switch (quality) {
          case 1:
            return '#fff';
          case 2:
            return '#00C724';
          case 3:
            return '#00BBFF';
          case 4:
            return '#9279FF';
          case 5:
            return '#FFD900';
          case 6:
            return '#FF9500';
          case 7:
            return '#FF2727';
          default:
            return '#fff';
        }
      }};
    pointer-events: none;
  }

  & > h3 {
    margin: 0;
    font-family: 'JetBrains Mono';
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1.98rem;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #140f08;
  }
  & > div {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 1.5rem;
    & > p {
      font-family: 'JetBrains Mono';
      font-size: 1.25rem;
      font-weight: 400;
      line-height: 1.65rem;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #686663;
      margin: 0;
      display: flex;
      & > span {
        flex: 1;
        color: #3f3b37;
      }
    }
  }
`;
