import { ComponentType } from '../Enum';
import { EntityComponent } from './EntityComponent';
import { game } from '@/game/Proto/generated/game_messages';
import * as THREE from 'three';

enum ActionType {
  UpdateTarget = 1,
  TransformPosition,
}

function decodePosition(data: number) {
  return data / 100 - 20;
}

function encodePosition(data: number) {
  return Math.floor((data + 20) * 100);
}

function decodeRotation(data: number) {
  return data / 1000 - 1;
}

function encodeRotation(data: number) {
  return Math.floor((data + 1) * 1000);
}

export class EntityFollowBone extends EntityComponent {
  targetClientIndex = 0;
  targetBoneName = '';
  targetServerId = 0;
  position = new THREE.Vector3();
  rotation = new THREE.Quaternion();

  private lastPosition: THREE.Vector3 | null = null;
  private lastRotation: THREE.Quaternion | null = null;

  override init() {
    super.init();

    this.registerAction(ActionType.UpdateTarget, game.CommonMessage, (data: game.CommonMessage) => {
      this.targetClientIndex = Number(data.messageList[0]);
      this.targetBoneName = data.messageList[1];
      this.targetServerId = Number(data.messageList[2]);
      this.selfUpdate();
    });

    this.registerAction(ActionType.TransformPosition, game.Transform, (data) => {
      this.position.set(decodePosition(data.x), decodePosition(data.y), decodePosition(data.z));
      this.rotation.set(
        decodeRotation(data.quaternionX),
        decodeRotation(data.quaternionY),
        decodeRotation(data.quaternionZ),
        decodeRotation(data.quaternionW)
      );
      this.selfUpdate();
    });
  }

  override getType() {
    return ComponentType.FollowBone;
  }

  override syncToServer() {
    this.sendTransformPosition();
    this.sendTarget();
  }

  private sendTransformPosition() {
    if (!this.lastPosition) {
      this.lastPosition = new THREE.Vector3();
    }
    if (!this.lastRotation) {
      this.lastRotation = new THREE.Quaternion();
    }
    this.lastPosition.copy(this.position);
    this.lastRotation.copy(this.rotation);
    this.sendAction(
      ActionType.TransformPosition,
      game.Transform.create({
        x: encodePosition(this.position.x),
        y: encodePosition(this.position.y),
        z: encodePosition(this.position.z),
        quaternionX: encodeRotation(this.rotation.x),
        quaternionY: encodeRotation(this.rotation.y),
        quaternionZ: encodeRotation(this.rotation.z),
        quaternionW: encodeRotation(this.rotation.w),
      }).toJSON()
    );
  }

  sendTarget() {
    this.sendAction(
      ActionType.UpdateTarget,
      game.CommonMessage.create({
        messageList: [String(this.targetClientIndex), this.targetBoneName],
      }).toJSON()
    );
  }

  setTarget(targetClientIndex: number, targetBoneName: string) {
    this.targetClientIndex = targetClientIndex;
    this.targetBoneName = targetBoneName;
    this.sendTarget();
  }

  setPosition(position: THREE.Vector3, rotation: THREE.Quaternion) {
    this.position.copy(position);
    this.rotation.copy(rotation);
    this.selfUpdate();

    if (
      !this.lastPosition ||
      !this.lastRotation ||
      this.position.distanceTo(this.lastPosition) > 0.1 ||
      this.rotation.angleTo(this.lastRotation) > 0.1
    ) {
      this.sendTransformPosition();
    }
  }
}
