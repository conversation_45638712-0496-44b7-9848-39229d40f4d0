import React, { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';
import { use<PERSON>rame } from '@react-three/fiber';
import { ParticleEditData, ParticleEditJson } from '@/game/TSX/Particles/ParticleEditor';
import ParticleNode from '@/game/TSX/Particles/ParticleNode';
import ParticleEditUI from '@/game/TSX/Particles/ParticleEditUI';

export default function ParticleEditObject({ particleData }: { particleData: ParticleEditData }) {
  const groupRef = useRef<THREE.Group>(null);
  const [config, setConfig] = useState<ParticleEditJson | null>(null);

  useEffect(() => {
    if (!groupRef.current) {
      return;
    }
    if (config === null) {
      return;
    }

    const time = config.minEmitTime === 0 && config.maxEmitTime === 0 ? 10 * 1000 : -1;
    const scale = 1;
    const defaultParticleNode = new ParticleNode(
      config.blending || THREE.AdditiveBlending,
      time > 0
    );
    groupRef.current.add(defaultParticleNode);
    defaultParticleNode.scale.set(scale, scale, scale);
    defaultParticleNode.name = 'particleEditNode';

    let stop = false;
    defaultParticleNode.initFromJson(config, () => {
      // defaultParticleNode.loadFromUrl(url);
      const clock = new THREE.Clock();

      if (time > 0) {
        setTimeout(() => {
          stop = true;
          defaultParticleNode.destroy();
        }, time);
      }
      const _renderLoop = function () {
        if (stop) {
          return;
        }
        defaultParticleNode.update(clock.getDelta());
        requestAnimationFrame(_renderLoop);
      };
      _renderLoop();
    });
    return () => {
      stop = true;
      defaultParticleNode.destroy();
    };
  }, [config]);

  useFrame((state) => {
    if (particleData.isEdit) {
      particleData.isEdit = false;
      if (particleData.config) {
        setConfig(null);
        setTimeout(() => {
          setConfig(particleData.config);
        }, 50);
      }
      if (groupRef.current) {
        groupRef.current.position.copy(particleData.position);
      }
    }
  });
  return (
    <group ref={groupRef}>
      <ParticleEditUI particleData={particleData} />
    </group>
  );
}
