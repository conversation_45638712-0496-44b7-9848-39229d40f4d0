import React, { useEffect, useState } from 'react';
import { ParticleEditData, InitPositionItem } from '@/game/TSX/Particles/ParticleEditor';
import { Html } from '@react-three/drei';
import { LoaderUtil } from '@/game/TSX/Util/LoaderUtil';
import * as THREE from 'three';
import { useFrame } from '@react-three/fiber';

export default function ParticleEditUI({ particleData }: { particleData: ParticleEditData }) {
  const [config, setConfig] = useState<any>(null);
  const [isVisible, setIsVisible] = useState(true);
  const fileInputId = `json-file-input-${particleData.uuid}`;
  const ref = React.useRef<THREE.Group>(null);
  useEffect(() => {
    LoaderUtil.loadJson('./particles/effect.json', (json: any) => {
      const clone = { ...json };
      particleData.config = clone;
      particleData.isEdit = true;
      setConfig(clone);
    });
  }, []);

  const replay = () => {
    particleData.config = { ...config };
    particleData.isEdit = true;
  };

  const updateConfig = (key: string, value: any) => {
    if (!config) return;

    const newConfig = { ...config };
    if (key.includes('.')) {
      const keys = key.split('.');
      let obj = newConfig;
      for (let i = 0; i < keys.length - 1; i++) {
        obj = obj[keys[i]];
      }
      obj[keys[keys.length - 1]] = value;
    } else {
      newConfig[key] = value;
    }

    setConfig(newConfig);
    particleData.config = newConfig;
    particleData.isEdit = true;
  };

  const updateArrayValue = (key: string, index: number, value: number) => {
    if (!config) return;
    const newConfig = { ...config };
    const keys = key.split('.');
    let obj = newConfig;
    for (let i = 0; i < keys.length - 1; i++) {
      obj = obj[keys[i]];
    }
    const array = [...obj[keys[keys.length - 1]]];
    array[index] = value;
    obj[keys[keys.length - 1]] = array;

    setConfig(newConfig);
    particleData.config = newConfig;
    particleData.isEdit = true;
  };

  // 添加初始位置设置项
  const addInitPositionItem = (type: 'box' | 'line' | 'sphere' | 'point') => {
    if (!config) return;

    const newConfig = { ...config };
    if (!newConfig.initPositionSet) {
      newConfig.initPositionSet = [];
    }

    let newItem: any;
    switch (type) {
      case 'box':
        newItem = {
          type: 'box',
          position: [0, 0, 0],
          width: 10,
          height: 10,
          depth: 10
        };
        break;
      case 'line':
        newItem = {
          type: 'line',
          positionStart: [0, 0, 0],
          positionEnd: [0, 0, 1]
        };
        break;
      case 'sphere':
        newItem = {
          type: 'sphere',
          position: [0, 0, 0],
          radius: 1
        };
        break;
      case 'point':
        newItem = {
          type: 'point',
          position: [0, 0, 0]
        };
        break;
    }

    newConfig.initPositionSet.push(newItem);
    setConfig(newConfig);
    particleData.config = newConfig;
    particleData.isEdit = true;
  };

  // 删除初始位置设置项
  const removeInitPositionItem = (index: number) => {
    if (!config || !config.initPositionSet) return;

    const newConfig = { ...config };
    newConfig.initPositionSet = [...newConfig.initPositionSet];
    newConfig.initPositionSet.splice(index, 1);

    setConfig(newConfig);
    particleData.config = newConfig;
    particleData.isEdit = true;
  };

  // 更新初始位置设置项
  const updateInitPositionItem = (index: number, field: string, value: any) => {
    if (!config || !config.initPositionSet) return;

    const newConfig = { ...config };
    newConfig.initPositionSet = [...newConfig.initPositionSet];
    const item = { ...newConfig.initPositionSet[index] };

    if (field.includes('.')) {
      const keys = field.split('.');
      let obj = item;
      for (let i = 0; i < keys.length - 1; i++) {
        if (!obj[keys[i]]) obj[keys[i]] = {};
        obj = obj[keys[i]];
      }
      obj[keys[keys.length - 1]] = value;
    } else {
      item[field] = value;
    }

    newConfig.initPositionSet[index] = item;
    setConfig(newConfig);
    particleData.config = newConfig;
    particleData.isEdit = true;
  };

  // 更新初始位置设置项的数组值
  const updateInitPositionArrayValue = (index: number, field: string, arrayIndex: number, value: number) => {
    if (!config || !config.initPositionSet) return;

    const newConfig = { ...config };
    newConfig.initPositionSet = [...newConfig.initPositionSet];
    const item = { ...newConfig.initPositionSet[index] };

    if (!item[field]) item[field] = [];
    const array = [...item[field]];
    array[arrayIndex] = value;
    item[field] = array;

    newConfig.initPositionSet[index] = item;
    setConfig(newConfig);
    particleData.config = newConfig;
    particleData.isEdit = true;
  };

  // 更新粒子系统全局位置
  const updateGlobalPosition = (axis: 'x' | 'y' | 'z', value: number) => {
    if (axis === 'x') {
      particleData.position.x = value;
    } else if (axis === 'y') {
      particleData.position.y = value;
    } else if (axis === 'z') {
      particleData.position.z = value;
    }
    particleData.isEdit = true;
  };
  // 在每一帧更新时，让元素朝向摄像机
  useFrame(({ camera }) => {
    if (ref.current) {
      // 让UI始终朝向摄像机，保持固定大小
      ref.current.lookAt(camera.position);

      // 保持固定的缩放，不受距离影响
      const distance = camera.position.distanceTo(ref.current.position);
      const scale = Math.max(0.5, Math.min(2.0, distance * 0.1)); // 限制缩放范围
      ref.current.scale.setScalar(scale);
    }
  });

  if (!config) return null;

  return (
    <group position={[0, 0, 0]} ref={ref}>
      <Html
        distanceFactor={1.5}
        transform
        pointerEvents="auto" // 启用鼠标事件以便交互
        position={[0, 0, 0]}
        style={{
          transformOrigin: 'bottom center',
          transform: 'translate(-00%,-60%)',
          transition: 'all 0.3s ease',
          width: '400px',
          maxHeight: '600px',
          overflow: 'auto',
        }}
        center={false}
      >
        <div
          style={{
            background: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            padding: '15px',
            borderRadius: '8px',
            fontSize: '12px',
            fontFamily: 'Arial, sans-serif',
            border: '1px solid #333',
            boxShadow: '0 4px 12px rgba(0,0,0,0.5)',
          }}
        >
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '15px',
              borderBottom: '1px solid #444',
              paddingBottom: '10px',
            }}
          >
            <h3 style={{ margin: 0, color: '#fff' }}>粒子效果编辑器</h3>
            <button
              onClick={() => replay()}
              style={{
                background: '#444',
                color: 'white',
                border: 'none',
                padding: '5px 10px',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              {'播放'}
            </button>
            <button
              onClick={() => setIsVisible(!isVisible)}
              style={{
                background: '#444',
                color: 'white',
                border: 'none',
                padding: '5px 10px',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              {isVisible ? '隐藏' : '显示'}
            </button>
          </div>

          {isVisible && (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
              {/* 全局位置 */}
              <div
                style={{
                  background: 'rgba(255,255,255,0.05)',
                  padding: '10px',
                  borderRadius: '4px',
                }}
              >
                <h4 style={{ margin: '0 0 10px 0', color: '#00BCD4' }}>粒子系统位置</h4>
                <div style={{ marginBottom: '8px' }}>
                  <label style={{ display: 'block', marginBottom: '4px' }}>全局位置 (X, Y, Z):</label>
                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '4px' }}>
                    <input
                      type="number"
                      step="0.1"
                      value={particleData.position.x}
                      onChange={(e) => updateGlobalPosition('x', parseFloat(e.target.value))}
                      style={{
                        width: '100%',
                        padding: '3px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                        fontSize: '11px',
                        boxSizing: 'border-box',
                      }}
                    />
                    <input
                      type="number"
                      step="0.1"
                      value={particleData.position.y}
                      onChange={(e) => updateGlobalPosition('y', parseFloat(e.target.value))}
                      style={{
                        width: '100%',
                        padding: '3px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                        fontSize: '11px',
                        boxSizing: 'border-box',
                      }}
                    />
                    <input
                      type="number"
                      step="0.1"
                      value={particleData.position.z}
                      onChange={(e) => updateGlobalPosition('z', parseFloat(e.target.value))}
                      style={{
                        width: '100%',
                        padding: '3px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                        fontSize: '11px',
                        boxSizing: 'border-box',
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* 发射参数 */}
              <div
                style={{
                  background: 'rgba(255,255,255,0.05)',
                  padding: '10px',
                  borderRadius: '4px',
                }}
              >
                <h4 style={{ margin: '0 0 10px 0', color: '#4CAF50' }}>发射参数</h4>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px' }}>
                  <label>
                    最小发射数量:
                    <input
                      type="number"
                      value={config.minEmitNumber || 0}
                      onChange={(e) => updateConfig('minEmitNumber', parseFloat(e.target.value))}
                      style={{
                        width: '100%',
                        padding: '4px',
                        marginTop: '2px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                      }}
                    />
                  </label>
                  <label>
                    最大发射数量:
                    <input
                      type="number"
                      value={config.maxEmitNumber || 0}
                      onChange={(e) => updateConfig('maxEmitNumber', parseFloat(e.target.value))}
                      style={{
                        width: '100%',
                        padding: '4px',
                        marginTop: '2px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                      }}
                    />
                  </label>
                  <label>
                    最小发射时间:
                    <input
                      type="number"
                      step="0.01"
                      value={config.minEmitTime || 0}
                      onChange={(e) => updateConfig('minEmitTime', parseFloat(e.target.value))}
                      style={{
                        width: '100%',
                        padding: '4px',
                        marginTop: '2px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                      }}
                    />
                  </label>
                  <label>
                    最大发射时间:
                    <input
                      type="number"
                      step="0.01"
                      value={config.maxEmitTime || 0}
                      onChange={(e) => updateConfig('maxEmitTime', parseFloat(e.target.value))}
                      style={{
                        width: '100%',
                        padding: '4px',
                        marginTop: '2px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                      }}
                    />
                  </label>
                </div>
              </div>

              {/* 粒子属性 */}
              <div
                style={{
                  background: 'rgba(255,255,255,0.05)',
                  padding: '10px',
                  borderRadius: '4px',
                }}
              >
                <h4 style={{ margin: '0 0 10px 0', color: '#2196F3' }}>粒子属性</h4>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px' }}>
                  <label>
                    最小半径:
                    <input
                      type="number"
                      step="0.01"
                      value={config.minRadius || 0}
                      onChange={(e) => updateConfig('minRadius', parseFloat(e.target.value))}
                      style={{
                        width: '100%',
                        padding: '4px',
                        marginTop: '2px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                      }}
                    />
                  </label>
                  <label>
                    最大半径:
                    <input
                      type="number"
                      step="0.01"
                      value={config.maxRadius || 0}
                      onChange={(e) => updateConfig('maxRadius', parseFloat(e.target.value))}
                      style={{
                        width: '100%',
                        padding: '4px',
                        marginTop: '2px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                      }}
                    />
                  </label>
                  <label>
                    最小生命:
                    <input
                      type="number"
                      step="0.1"
                      value={config.minLife || 0}
                      onChange={(e) => updateConfig('minLife', parseFloat(e.target.value))}
                      style={{
                        width: '100%',
                        padding: '4px',
                        marginTop: '2px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                      }}
                    />
                  </label>
                  <label>
                    最大生命:
                    <input
                      type="number"
                      step="0.1"
                      value={config.maxLife || 0}
                      onChange={(e) => updateConfig('maxLife', parseFloat(e.target.value))}
                      style={{
                        width: '100%',
                        padding: '4px',
                        marginTop: '2px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                      }}
                    />
                  </label>
                  <label>
                    最小质量:
                    <input
                      type="number"
                      step="0.1"
                      value={config.minMass || 0}
                      onChange={(e) => updateConfig('minMass', parseFloat(e.target.value))}
                      style={{
                        width: '100%',
                        padding: '4px',
                        marginTop: '2px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                      }}
                    />
                  </label>
                  <label>
                    最大质量:
                    <input
                      type="number"
                      step="0.1"
                      value={config.maxMass || 0}
                      onChange={(e) => updateConfig('maxMass', parseFloat(e.target.value))}
                      style={{
                        width: '100%',
                        padding: '4px',
                        marginTop: '2px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                      }}
                    />
                  </label>
                </div>
              </div>

              {/* 速度设置 */}
              <div
                style={{
                  background: 'rgba(255,255,255,0.05)',
                  padding: '10px',
                  borderRadius: '4px',
                }}
              >
                <h4 style={{ margin: '0 0 10px 0', color: '#FF9800' }}>速度设置</h4>
                <div style={{ marginBottom: '8px' }}>
                  <label style={{ display: 'block', marginBottom: '4px' }}>方向 (X, Y, Z):</label>
                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '4px' }}>
                    <input
                      type="number"
                      step="0.1"
                      value={config.velocity?.dir?.[0] || 0}
                      onChange={(e) =>
                        updateArrayValue('velocity.dir', 0, parseFloat(e.target.value))
                      }
                      style={{
                        width: '100%',
                        padding: '3px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                        fontSize: '11px',
                        boxSizing: 'border-box',
                      }}
                    />
                    <input
                      type="number"
                      step="0.1"
                      value={config.velocity?.dir?.[1] || 0}
                      onChange={(e) =>
                        updateArrayValue('velocity.dir', 1, parseFloat(e.target.value))
                      }
                      style={{
                        width: '100%',
                        padding: '3px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                        fontSize: '11px',
                        boxSizing: 'border-box',
                      }}
                    />
                    <input
                      type="number"
                      step="0.1"
                      value={config.velocity?.dir?.[2] || 0}
                      onChange={(e) =>
                        updateArrayValue('velocity.dir', 2, parseFloat(e.target.value))
                      }
                      style={{
                        width: '100%',
                        padding: '3px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                        fontSize: '11px',
                        boxSizing: 'border-box',
                      }}
                    />
                  </div>
                </div>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '8px' }}>
                  <label>
                    最小速度:
                    <input
                      type="number"
                      step="0.1"
                      value={config.velocity?.minSpeed || 0}
                      onChange={(e) =>
                        updateConfig('velocity.minSpeed', parseFloat(e.target.value))
                      }
                      style={{
                        width: '100%',
                        padding: '4px',
                        marginTop: '2px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                      }}
                    />
                  </label>
                  <label>
                    最大速度:
                    <input
                      type="number"
                      step="0.1"
                      value={config.velocity?.maxSpeed || 0}
                      onChange={(e) =>
                        updateConfig('velocity.maxSpeed', parseFloat(e.target.value))
                      }
                      style={{
                        width: '100%',
                        padding: '4px',
                        marginTop: '2px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                      }}
                    />
                  </label>
                  <label>
                    角度:
                    <input
                      type="number"
                      value={config.velocity?.tha || 0}
                      onChange={(e) => updateConfig('velocity.tha', parseFloat(e.target.value))}
                      style={{
                        width: '100%',
                        padding: '4px',
                        marginTop: '2px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                      }}
                    />
                  </label>
                </div>
              </div>

              {/* 初始位置设置 */}
              <div
                style={{
                  background: 'rgba(255,255,255,0.05)',
                  padding: '10px',
                  borderRadius: '4px',
                }}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
                  <h4 style={{ margin: '0', color: '#E91E63' }}>初始位置设置</h4>
                  <div style={{ display: 'flex', gap: '4px' }}>
                    <button
                      onClick={() => addInitPositionItem('box')}
                      style={{
                        background: '#E91E63',
                        color: 'white',
                        border: 'none',
                        padding: '4px 8px',
                        borderRadius: '2px',
                        cursor: 'pointer',
                        fontSize: '10px',
                      }}
                    >
                      +盒子
                    </button>
                    <button
                      onClick={() => addInitPositionItem('line')}
                      style={{
                        background: '#E91E63',
                        color: 'white',
                        border: 'none',
                        padding: '4px 8px',
                        borderRadius: '2px',
                        cursor: 'pointer',
                        fontSize: '10px',
                      }}
                    >
                      +线段
                    </button>
                    <button
                      onClick={() => addInitPositionItem('sphere')}
                      style={{
                        background: '#E91E63',
                        color: 'white',
                        border: 'none',
                        padding: '4px 8px',
                        borderRadius: '2px',
                        cursor: 'pointer',
                        fontSize: '10px',
                      }}
                    >
                      +球体
                    </button>
                    <button
                      onClick={() => addInitPositionItem('point')}
                      style={{
                        background: '#E91E63',
                        color: 'white',
                        border: 'none',
                        padding: '4px 8px',
                        borderRadius: '2px',
                        cursor: 'pointer',
                        fontSize: '10px',
                      }}
                    >
                      +点
                    </button>
                  </div>
                </div>

                {config.initPositionSet && config.initPositionSet.length > 0 && (
                  <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                    {config.initPositionSet.map((item: any, index: number) => (
                      <div
                        key={index}
                        style={{
                          background: 'rgba(255,255,255,0.03)',
                          padding: '8px',
                          borderRadius: '4px',
                          marginBottom: '8px',
                          border: '1px solid #444',
                        }}
                      >
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                          <span style={{ color: '#E91E63', fontWeight: 'bold', fontSize: '11px' }}>
                            {item.type.toUpperCase()} #{index + 1}
                          </span>
                          <button
                            onClick={() => removeInitPositionItem(index)}
                            style={{
                              background: '#f44336',
                              color: 'white',
                              border: 'none',
                              padding: '2px 6px',
                              borderRadius: '2px',
                              cursor: 'pointer',
                              fontSize: '10px',
                            }}
                          >
                            删除
                          </button>
                        </div>

                        {/* 根据类型渲染不同的编辑界面 */}
                        {item.type === 'box' && (
                          <div>
                            <div style={{ marginBottom: '6px' }}>
                              <label style={{ display: 'block', marginBottom: '2px', fontSize: '10px' }}>位置 (X, Y, Z):</label>
                              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '2px' }}>
                                {[0, 1, 2].map((i) => (
                                  <input
                                    key={i}
                                    type="number"
                                    step="0.1"
                                    value={item.position?.[i] || 0}
                                    onChange={(e) => updateInitPositionArrayValue(index, 'position', i, parseFloat(e.target.value))}
                                    style={{
                                      width: '100%',
                                      padding: '2px',
                                      background: '#333',
                                      color: 'white',
                                      border: '1px solid #555',
                                      borderRadius: '2px',
                                      fontSize: '10px',
                                      boxSizing: 'border-box',
                                    }}
                                  />
                                ))}
                              </div>
                            </div>
                            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '4px' }}>
                              <label style={{ fontSize: '10px' }}>
                                宽度:
                                <input
                                  type="number"
                                  step="0.1"
                                  value={item.width || 0}
                                  onChange={(e) => updateInitPositionItem(index, 'width', parseFloat(e.target.value))}
                                  style={{
                                    width: '100%',
                                    padding: '2px',
                                    marginTop: '2px',
                                    background: '#333',
                                    color: 'white',
                                    border: '1px solid #555',
                                    borderRadius: '2px',
                                    fontSize: '10px',
                                  }}
                                />
                              </label>
                              <label style={{ fontSize: '10px' }}>
                                高度:
                                <input
                                  type="number"
                                  step="0.1"
                                  value={item.height || 0}
                                  onChange={(e) => updateInitPositionItem(index, 'height', parseFloat(e.target.value))}
                                  style={{
                                    width: '100%',
                                    padding: '2px',
                                    marginTop: '2px',
                                    background: '#333',
                                    color: 'white',
                                    border: '1px solid #555',
                                    borderRadius: '2px',
                                    fontSize: '10px',
                                  }}
                                />
                              </label>
                              <label style={{ fontSize: '10px' }}>
                                深度:
                                <input
                                  type="number"
                                  step="0.1"
                                  value={item.depth || 0}
                                  onChange={(e) => updateInitPositionItem(index, 'depth', parseFloat(e.target.value))}
                                  style={{
                                    width: '100%',
                                    padding: '2px',
                                    marginTop: '2px',
                                    background: '#333',
                                    color: 'white',
                                    border: '1px solid #555',
                                    borderRadius: '2px',
                                    fontSize: '10px',
                                  }}
                                />
                              </label>
                            </div>
                          </div>
                        )}

                        {item.type === 'line' && (
                          <div>
                            <div style={{ marginBottom: '6px' }}>
                              <label style={{ display: 'block', marginBottom: '2px', fontSize: '10px' }}>起始位置 (X, Y, Z):</label>
                              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '2px' }}>
                                {[0, 1, 2].map((i) => (
                                  <input
                                    key={i}
                                    type="number"
                                    step="0.1"
                                    value={item.positionStart?.[i] || 0}
                                    onChange={(e) => updateInitPositionArrayValue(index, 'positionStart', i, parseFloat(e.target.value))}
                                    style={{
                                      width: '100%',
                                      padding: '2px',
                                      background: '#333',
                                      color: 'white',
                                      border: '1px solid #555',
                                      borderRadius: '2px',
                                      fontSize: '10px',
                                      boxSizing: 'border-box',
                                    }}
                                  />
                                ))}
                              </div>
                            </div>
                            <div style={{ marginBottom: '6px' }}>
                              <label style={{ display: 'block', marginBottom: '2px', fontSize: '10px' }}>结束位置 (X, Y, Z):</label>
                              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '2px' }}>
                                {[0, 1, 2].map((i) => (
                                  <input
                                    key={i}
                                    type="number"
                                    step="0.1"
                                    value={item.positionEnd?.[i] || 0}
                                    onChange={(e) => updateInitPositionArrayValue(index, 'positionEnd', i, parseFloat(e.target.value))}
                                    style={{
                                      width: '100%',
                                      padding: '2px',
                                      background: '#333',
                                      color: 'white',
                                      border: '1px solid #555',
                                      borderRadius: '2px',
                                      fontSize: '10px',
                                      boxSizing: 'border-box',
                                    }}
                                  />
                                ))}
                              </div>
                            </div>
                          </div>
                        )}

                        {item.type === 'sphere' && (
                          <div>
                            <div style={{ marginBottom: '6px' }}>
                              <label style={{ display: 'block', marginBottom: '2px', fontSize: '10px' }}>位置 (X, Y, Z):</label>
                              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '2px' }}>
                                {[0, 1, 2].map((i) => (
                                  <input
                                    key={i}
                                    type="number"
                                    step="0.1"
                                    value={item.position?.[i] || 0}
                                    onChange={(e) => updateInitPositionArrayValue(index, 'position', i, parseFloat(e.target.value))}
                                    style={{
                                      width: '100%',
                                      padding: '2px',
                                      background: '#333',
                                      color: 'white',
                                      border: '1px solid #555',
                                      borderRadius: '2px',
                                      fontSize: '10px',
                                      boxSizing: 'border-box',
                                    }}
                                  />
                                ))}
                              </div>
                            </div>
                            <label style={{ fontSize: '10px' }}>
                              半径:
                              <input
                                type="number"
                                step="0.1"
                                value={item.radius || 0}
                                onChange={(e) => updateInitPositionItem(index, 'radius', parseFloat(e.target.value))}
                                style={{
                                  width: '100%',
                                  padding: '2px',
                                  marginTop: '2px',
                                  background: '#333',
                                  color: 'white',
                                  border: '1px solid #555',
                                  borderRadius: '2px',
                                  fontSize: '10px',
                                }}
                              />
                            </label>
                          </div>
                        )}

                        {item.type === 'point' && (
                          <div>
                            <div style={{ marginBottom: '6px' }}>
                              <label style={{ display: 'block', marginBottom: '2px', fontSize: '10px' }}>位置 (X, Y, Z):</label>
                              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '2px' }}>
                                {[0, 1, 2].map((i) => (
                                  <input
                                    key={i}
                                    type="number"
                                    step="0.1"
                                    value={item.position?.[i] || 0}
                                    onChange={(e) => updateInitPositionArrayValue(index, 'position', i, parseFloat(e.target.value))}
                                    style={{
                                      width: '100%',
                                      padding: '2px',
                                      background: '#333',
                                      color: 'white',
                                      border: '1px solid #555',
                                      borderRadius: '2px',
                                      fontSize: '10px',
                                      boxSizing: 'border-box',
                                    }}
                                  />
                                ))}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}

                {(!config.initPositionSet || config.initPositionSet.length === 0) && (
                  <div style={{
                    textAlign: 'center',
                    color: '#666',
                    fontSize: '11px',
                    padding: '20px',
                    fontStyle: 'italic'
                  }}>
                    暂无初始位置设置，点击上方按钮添加
                  </div>
                )}
              </div>

              {/* 物理效果 */}
              <div
                style={{
                  background: 'rgba(255,255,255,0.05)',
                  padding: '10px',
                  borderRadius: '4px',
                }}
              >
                <h4 style={{ margin: '0 0 10px 0', color: '#9C27B0' }}>物理效果</h4>
                <label>
                  重力:
                  <input
                    type="number"
                    step="0.01"
                    value={config.gravity || 0}
                    onChange={(e) => updateConfig('gravity', parseFloat(e.target.value))}
                    style={{
                      width: '100%',
                      padding: '4px',
                      marginTop: '2px',
                      background: '#333',
                      color: 'white',
                      border: '1px solid #555',
                      borderRadius: '2px',
                    }}
                  />
                </label>
              </div>

              {/* 视觉效果 */}
              <div
                style={{
                  background: 'rgba(255,255,255,0.05)',
                  padding: '10px',
                  borderRadius: '4px',
                }}
              >
                <h4 style={{ margin: '0 0 10px 0', color: '#F44336' }}>视觉效果</h4>
                <div
                  style={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr',
                    gap: '8px',
                    marginBottom: '8px',
                  }}
                >
                  <label>
                    初始透明度:
                    <input
                      type="number"
                      step="0.1"
                      min="0"
                      max="1"
                      value={config.alphaStart || 0}
                      onChange={(e) => updateConfig('alphaStart', parseFloat(e.target.value))}
                      style={{
                        width: '100%',
                        padding: '4px',
                        marginTop: '2px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                      }}
                    />
                  </label>
                  <label>
                    结束透明度:
                    <input
                      type="number"
                      step="0.1"
                      min="0"
                      max="1"
                      value={config.alphaEnd || 0}
                      onChange={(e) => updateConfig('alphaEnd', parseFloat(e.target.value))}
                      style={{
                        width: '100%',
                        padding: '4px',
                        marginTop: '2px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                      }}
                    />
                  </label>
                  <label>
                    初始缩放:
                    <input
                      type="number"
                      step="0.1"
                      value={config.scaleStart || 0}
                      onChange={(e) => updateConfig('scaleStart', parseFloat(e.target.value))}
                      style={{
                        width: '100%',
                        padding: '4px',
                        marginTop: '2px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                      }}
                    />
                  </label>
                  <label>
                    结束缩放:
                    <input
                      type="number"
                      step="0.1"
                      value={config.scaleEnd || 0}
                      onChange={(e) => updateConfig('scaleEnd', parseFloat(e.target.value))}
                      style={{
                        width: '100%',
                        padding: '4px',
                        marginTop: '2px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                      }}
                    />
                  </label>
                </div>
                <div
                  style={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr',
                    gap: '8px',
                    marginBottom: '8px',
                  }}
                >
                  <label>
                    起始颜色:
                    <input
                      type="color"
                      value={config.startColor || '#ffffff'}
                      onChange={(e) => updateConfig('startColor', e.target.value)}
                      style={{
                        width: '100%',
                        padding: '2px',
                        marginTop: '2px',
                        background: '#333',
                        border: '1px solid #555',
                        borderRadius: '2px',
                      }}
                    />
                  </label>
                  <label>
                    结束颜色:
                    <input
                      type="color"
                      value={config.endColor || '#ffffff'}
                      onChange={(e) => updateConfig('endColor', e.target.value)}
                      style={{
                        width: '100%',
                        padding: '2px',
                        marginTop: '2px',
                        background: '#333',
                        border: '1px solid #555',
                        borderRadius: '2px',
                      }}
                    />
                  </label>
                </div>
                <div style={{ display: 'flex', gap: '15px', fontSize: '11px' }}>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
                    <input
                      type="checkbox"
                      checked={config.isStartColorRandom || false}
                      onChange={(e) => updateConfig('isStartColorRandom', e.target.checked)}
                    />
                    起始颜色随机
                  </label>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
                    <input
                      type="checkbox"
                      checked={config.isEndColorRandom || false}
                      onChange={(e) => updateConfig('isEndColorRandom', e.target.checked)}
                    />
                    结束颜色随机
                  </label>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
                    <input
                      type="checkbox"
                      checked={config.enableEndColor || false}
                      onChange={(e) => updateConfig('enableEndColor', e.target.checked)}
                    />
                    启用结束颜色
                  </label>
                </div>
              </div>

              {/* 漂移效果 */}
              <div
                style={{
                  background: 'rgba(255,255,255,0.05)',
                  padding: '10px',
                  borderRadius: '4px',
                }}
              >
                <h4 style={{ margin: '0 0 10px 0', color: '#607D8B' }}>漂移效果</h4>
                <div style={{ marginBottom: '8px' }}>
                  <label style={{ display: 'block', marginBottom: '4px' }}>漂移力 (X, Y, Z):</label>
                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '4px' }}>
                    <input
                      type="number"
                      step="0.01"
                      value={config.drift?.force?.[0] || 0}
                      onChange={(e) =>
                        updateArrayValue('drift.force', 0, parseFloat(e.target.value))
                      }
                      style={{
                        width: '100%',
                        padding: '3px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                        fontSize: '11px',
                        boxSizing: 'border-box',
                      }}
                    />
                    <input
                      type="number"
                      step="0.01"
                      value={config.drift?.force?.[1] || 0}
                      onChange={(e) =>
                        updateArrayValue('drift.force', 1, parseFloat(e.target.value))
                      }
                      style={{
                        width: '100%',
                        padding: '3px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                        fontSize: '11px',
                        boxSizing: 'border-box',
                      }}
                    />
                    <input
                      type="number"
                      step="0.01"
                      value={config.drift?.force?.[2] || 0}
                      onChange={(e) =>
                        updateArrayValue('drift.force', 2, parseFloat(e.target.value))
                      }
                      style={{
                        width: '100%',
                        padding: '3px',
                        background: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        borderRadius: '2px',
                        fontSize: '11px',
                        boxSizing: 'border-box',
                      }}
                    />
                  </div>
                </div>
                <label>
                  漂移间隔:
                  <input
                    type="number"
                    step="0.01"
                    value={config.drift?.interval || 0}
                    onChange={(e) => updateConfig('drift.interval', parseFloat(e.target.value))}
                    style={{
                      width: '100%',
                      padding: '4px',
                      marginTop: '2px',
                      background: '#333',
                      color: 'white',
                      border: '1px solid #555',
                      borderRadius: '2px',
                    }}
                  />
                </label>
              </div>

              {/* 操作按钮 */}
              <div
                style={{
                  display: 'flex',
                  gap: '8px',
                  justifyContent: 'center',
                  paddingTop: '10px',
                  borderTop: '1px solid #444',
                }}
              >
                <input
                  type="file"
                  accept=".json"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      const reader = new FileReader();
                      reader.onload = (event) => {
                        try {
                          const jsonConfig = JSON.parse(event.target?.result as string);
                          setConfig(jsonConfig);
                          particleData.config = jsonConfig;
                          particleData.isEdit = true;
                        } catch (error) {
                          alert('JSON文件格式错误，请检查文件内容');
                          console.error('JSON解析错误:', error);
                        }
                      };
                      reader.readAsText(file);
                    }
                    // 清空input值，允许重复选择同一文件
                    e.target.value = '';
                  }}
                  style={{ display: 'none' }}
                  id={fileInputId}
                />
                <button
                  onClick={() => {
                    document.getElementById(fileInputId)?.click();
                  }}
                  style={{
                    background: '#2196F3',
                    color: 'white',
                    border: 'none',
                    padding: '8px 16px',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '12px',
                  }}
                >
                  读取配置
                </button>
                <button
                  onClick={() => {
                    // 创建下载链接
                    const configJson = JSON.stringify(config, null, 2);
                    const blob = new Blob([configJson], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);

                    // 创建临时下载链接
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `particle-config-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    // 清理URL对象
                    URL.revokeObjectURL(url);
                  }}
                  style={{
                    background: '#4CAF50',
                    color: 'white',
                    border: 'none',
                    padding: '8px 16px',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '12px',
                  }}
                >
                  保存配置
                </button>
              </div>
            </div>
          )}
        </div>
      </Html>
    </group>
  );
}
