import React, { useEffect } from 'react';
import * as THREE from 'three';
import { useFrame } from '@react-three/fiber';

import { LoaderUtil, preloadParticleJson } from '@/game/TSX/Util/LoaderUtil';
import { generateUUID } from 'three/src/math/MathUtils';
import ParticleNode from '@/game/TSX/Particles/ParticleNode';

class ParticleItem {
  id: string;
  private url: string;
  private scale: number;
  private time: number;
  private position: THREE.Vector3;
  private quaternion: THREE.Quaternion;
  private replace: boolean;

  constructor(
    position: THREE.Vector3,
    quaternion: THREE.Quaternion,
    url: string,
    scale: number,
    time: number,
    replace: boolean
  ) {
    this.id = generateUUID();
    this.position = position;
    this.quaternion = quaternion;
    this.url = url;
    this.scale = scale;
    this.time = time;
    this.replace = replace;

    preloadParticleJson();
  }

  play(endCallback?: () => void) {
    const defaultParticleNode = new ParticleNode(!this.replace);
    defaultParticleNode.scale.set(this.scale, this.scale, this.scale);
    defaultParticleNode.name = 'particleNode';
    defaultParticleNode.position.copy(this.position);
    defaultParticleNode.quaternion.copy(this.quaternion);
    LoaderUtil.loadJson(this.url, (json) => {
      defaultParticleNode.initFromJson(json, () => {
        let stop = false;
        const clock = new THREE.Clock();
        if (this.time > 0) {
          setTimeout(() => {
            stop = true;
            defaultParticleNode.destroy();
            if (endCallback) {
              endCallback();
            }
          }, this.time);
        }
        const _renderLoop = function () {
          if (stop) {
            return;
          }
          defaultParticleNode.update(clock.getDelta());
          requestAnimationFrame(_renderLoop);
        };
        _renderLoop();
      });
    });
    return defaultParticleNode;
  }
}

class ParticleSystemClass {
  particleItemMap: Map<string, ParticleNode> = new Map();

  private clock = new THREE.Clock();

  addParticle(
    position: THREE.Vector3,
    quaternion: THREE.Quaternion,
    url: string,
    scale: number,
    time: number,
    replace = false
  ) {
    const item = new ParticleItem(position, quaternion, url, scale, time, replace);
    const node = item.play(() => {
      this.removeParticleCallback(node);
      // this.particleItemMap.delete(item.id)
    });
    this.addParticleCallback(node);
    // this.particleItemMap.set(item.id, node)
  }

  registerParticleChange(
    addParticle: (effectObject: THREE.Object3D) => void,
    removeParticle: (effectObject: THREE.Object3D) => void
  ) {
    this.addParticleCallback = addParticle;
    this.removeParticleCallback = removeParticle;
  }

  updateParticle() {
    const delta = this.clock.getDelta();
    this.particleItemMap.forEach((node) => {
      node.update(delta);
    });
  }

  private addParticleCallback: (effectObject: THREE.Object3D) => void = () => undefined;

  private removeParticleCallback: (effectObject: THREE.Object3D) => void = () => undefined;
}

const ParticleSystemUtil = new ParticleSystemClass();

export function getParticleSystem() {
  return ParticleSystemUtil;
}

export default function ParticleSystem() {
  const groupRef = React.useRef<THREE.Group>(null);
  const particleSystemUtil = getParticleSystem();

  useEffect(() => {
    if (!groupRef.current) {
      return;
    }
    const group = groupRef.current;
    particleSystemUtil.registerParticleChange(
      (node) => {
        group.add(node);
      },
      (node) => {
        group.remove(node);
      }
    );
    return () => {
      particleSystemUtil.registerParticleChange(
        (node) => undefined,
        (node) => undefined
      );
    };
  }, []);

  useFrame((state) => {
    particleSystemUtil.updateParticle();
  });

  return <group ref={groupRef}></group>;
}
